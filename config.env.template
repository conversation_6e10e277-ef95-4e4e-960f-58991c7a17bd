# 联通API配置
UNICOM_API_URL=https://card.10010.com/NumDockerApp/NumberCenter/qryNum/newRecToken
# 重要：从联通APP任意抓包，获取请求头中Cookie字段的 ecs_token 值
ECS_TOKEN=
PROVINCE_CODE=17
CITY_CODE=166
NUM_CLASS=1
AMOUNTS=100
CHANNEL=B2C
CHNL_TYPE=1
GROUP_TYPE=1
CALL_TYPE=1
MONITOR_PURPOSE=1009
SEARCH_RATIO=100
SEARCH_CODE=02
SEARCH_TYPE=1
# SELECT_RULE=101,201,202,301,302
SELECT_RULE=306,307,308,310,311,315,316,317,318,322

# 数据库配置 (MySQL)
DB_HOST=
DB_PORT=3306
DB_USER=
DB_PASSWORD=
DB_NAME=

# 钉钉机器人配置
DINGTALK_WEBHOOK=
DINGTALK_SECRET=

# 监控配置
REQUEST_INTERVAL=20  # 请求间隔（秒）
MAX_RETRIES=3      # 最大重试次数
RETRY_DELAY=2      # 重试延迟（秒）

# 数据存储配置
# DATA_DIR=data
# HISTORY_FILE=phone_numbers_history.json
# RESULTS_FILE=special_numbers.csv

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/monitor.log