# 联通手机号监控系统

一个生产级的联通手机号监控系统，具备完善的错误处理、网络重试、数据库连接池等企业级特性。支持识别尾号3A、全段4A、回旋号、顺子等特殊号码类型，并通过钉钉机器人发送通知。

## 功能特性

### 🎯 核心功能
- **持续监控**: 单线程持续监控联通手机号API接口
- **智能筛选**: 识别多种特殊号码类型
- **去重机制**: 避免重复记录和通知相同号码
- **钉钉通知**: 发现特殊号码时自动发送钉钉通知
- **数据存储**: 支持MySQL数据库存储，自动fallback到文件存储

### 🚀 企业级特性
- **Token缓存**: 智能Token管理，支持数据库缓存和自动刷新
- **连接池**: MySQL连接池管理，支持断线重连和健康检查
- **网络重试**: 指数退避重试策略，支持网络状态检测
- **错误恢复**: 完善的异常处理和自动恢复机制
- **生产就绪**: 适合7x24小时稳定运行

### 📱 支持的特殊号码类型
- **尾号3A**: 如 1111、2222、3333 等
- **全段4A**: 如 1111-1111-111、2222-2222-222 等
- **尾号双回旋**: 如 1212、3434、5656 等
- **尾号三回旋**: 如 401401、824824 等
- **尾号4顺子**: 如 1234、5678、9876 等
- **全段5顺子**: 如 12345、56789、98765 等

### 🔧 技术特性
- **请求间隔**: 可配置的请求延迟机制（默认20秒）
- **重试机制**: 指数退避重试策略（2s, 4s, 8s...）
- **超时处理**: 针对超时错误的额外重试机制
- **网络检测**: 自动检测网络状态，等待网络恢复
- **JSONP解析**: 完整解析API响应格式
- **异常处理**: 全面的错误处理和日志记录
- **优雅停止**: 支持信号处理和优雅关闭
- **健康检查**: 数据库连接健康检查和自动重连

## 项目结构

```
10010/
├── src/
│   └── unicom_monitor/
│       ├── __init__.py          # 包初始化
│       ├── config.py            # 配置管理
│       ├── api_client.py        # API客户端（支持网络重试）
│       ├── number_analyzer.py   # 号码分析器
│       ├── data_storage.py      # 数据存储（MySQL + 文件备份）
│       ├── dingtalk_notifier.py # 钉钉通知（支持重试）
│       ├── token_manager.py     # Token管理（支持缓存）
│       ├── network_utils.py     # 网络工具（重试、检测）
│       ├── db_manager.py        # 数据库管理（连接池）
│       └── monitor.py           # 主监控程序
├── data/                        # 文件存储目录（fallback）
├── logs/                        # 日志目录
├── main.py                      # 主程序入口
├── requirements.txt             # 依赖包列表
├── config.env.template          # 配置文件模板
├── run.sh                       # Linux/Mac运行脚本
├── run.bat                      # Windows运行脚本
└── README.md                    # 项目说明
```

## 快速开始

### 1. 环境准备

确保已安装Python 3.8+：

```bash
python --version
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置设置

复制配置模板并编辑：

```bash
cp config.env.template config.env
```

编辑 `config.env` 文件，配置MySQL数据库、钉钉机器人和联通 `ecs_token`：

```env
# 数据库配置 (MySQL)
DB_HOST=*************
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=10010

# 重要：从联通APP任意抓包，获取请求头中Cookie字段的 ecs_token 值
ECS_TOKEN=your_ecs_token_here

# 钉钉机器人配置
DINGTALK_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=YOUR_ACCESS_TOKEN
DINGTALK_SECRET=YOUR_SECRET

# 监控配置
REQUEST_INTERVAL=5  # 请求间隔（秒）
MAX_RETRIES=3      # 最大重试次数
```

### 4. 演示功能

运行演示脚本查看号码分析功能：

```bash
python demo.py
```

### 5. 运行程序

#### 持续监控模式
```bash
python main.py
```

#### 单次检查模式
```bash
python main.py --once
```

#### 测试钉钉连接
```bash
python main.py --test
```

#### 查看统计信息
```bash
python main.py --stats
```

#### 导出数据
```bash
python main.py --export output.json
```

#### 使用运行脚本（推荐）

**Linux/Mac:**
```bash
# 初始化环境
./run.sh setup

# 启动监控
./run.sh start

# 单次检查
./run.sh once
```

**Windows:**
```cmd
# 初始化环境
run.bat setup

# 启动监控
run.bat start

# 单次检查
run.bat once
```

## 配置说明

### 联通API配置
- `ECS_TOKEN`: **（必需）** 从联通APP抓包获取的用户身份凭证，用于自动刷新接口`TOKEN`。
- `UNICOM_API_URL`: 联通API地址（一般无需修改）。
- `PROVINCE_CODE`: 省份代码（默认17）。
- `CITY_CODE`: 城市代码（默认166）。
- `AMOUNTS`: 每次获取数量（默认100）

### 数据库配置
- `DB_HOST`: MySQL数据库主机地址
- `DB_PORT`: 数据库端口
- `DB_USER`: 数据库用户名
- `DB_PASSWORD`: 数据库密码
- `DB_NAME`: 数据库名称

### 钉钉机器人配置
1. 在钉钉群中添加自定义机器人
2. 获取webhook地址和secret密钥

### 监控配置
- `REQUEST_INTERVAL`: 请求间隔时间（秒）
- `MAX_RETRIES`: 最大重试次数
- `RETRY_DELAY`: 重试延迟时间（秒）

### 数据存储配置
- 本项目已从文件存储迁移到MySQL数据库，请正确配置数据库连接信息。

## 使用示例

### 基本使用
```bash
# 启动监控（持续运行）
python main.py

# 执行单次检查
python main.py --once

# 使用自定义配置文件
python main.py --config my_config.env
```

### 数据管理
```bash
# 查看统计信息
python main.py --stats

# 导出所有数据
python main.py --export

# 导出到指定文件
python main.py --export backup_20240703.json
```

### 调试和测试
```bash
# 启用详细日志
python main.py --verbose

# 测试钉钉连接
python main.py --test
```

## 数据格式

### 数据库表结构

#### 1. `special_numbers` - 特殊号码记录
| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| `id` | INT | 自增主键 |
| `number` | VARCHAR(11) | 手机号码 |
| `type` | VARCHAR(50) | 特殊号码类型 |
| `pattern` | VARCHAR(50) | 匹配的模式 |
| `description` | VARCHAR(255)| 号码描述 |
| `discovered_time` | TIMESTAMP | 发现时间 |

#### 2. `processed_numbers` - 已处理号码记录
| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| `number` | VARCHAR(11) | 已处理的手机号码（主键） |
| `processed_time`| TIMESTAMP | 处理时间 |

## 日志说明

程序会在 `logs/monitor.log` 文件中记录详细的运行日志，包括：
- API请求和响应信息
- 号码分析结果
- 钉钉通知发送状态
- 错误和异常信息

## 注意事项

1. **请求频率**: 建议设置合理的请求间隔（3-5秒），避免对服务器造成压力
2. **网络环境**: 确保网络连接稳定，程序会自动重试失败的请求
3. **数据库连接**: 确保程序运行环境可以正常访问您配置的MySQL数据库
4. **存储空间**: 长期运行会积累大量数据，请关注数据库的存储空间
5. **钉钉限制**: 钉钉机器人有发送频率限制，避免短时间内大量通知

## 故障排除

### 常见问题

1. **配置文件未找到**
   ```
   ❌ 配置文件未找到: [Errno 2] No such file or directory: 'config.env'
   ```
   解决方案：确保 `config.env` 文件存在，或使用 `--config` 指定正确路径

2. **钉钉通知失败**
   ```
   钉钉消息发送失败: {'errcode': 300005, 'errmsg': 'token is not exist'}
   ```
   解决方案：检查钉钉机器人配置，参考 `DINGTALK_SETUP.md` 文件

3. **API请求失败**
   ```
   API返回错误: M10 - 系统错误
   ```
   解决方案：联通API的`TOKEN`已实现自动刷新，如果持续失败，请检查`config.env`中的`ECS_TOKEN`是否已过期，需要重新抓包获取。

4. **Token过期问题**
   ```
   数据解析失败: 无法解析API响应
   ```
   解决方案：联通API的`TOKEN`已实现自动刷新，如果持续失败，请检查`config.env`中的`ECS_TOKEN`是否已过期，需要重新抓包获取。

5. **网络连接问题**
   ```
   网络请求失败: HTTPSConnectionPool(host='...', port=443): Read timed out
   ```
   解决方案：系统会自动检测网络状态并重试，支持指数退避策略。如果网络长时间不可用，程序会等待网络恢复。

6. **数据库连接问题**
   ```
   MySQL连接失败: (2003, "Can't connect to MySQL server")
   ```
   解决方案：系统会自动fallback到文件存储。检查MySQL服务状态和配置，系统支持自动重连。

### 企业级特性

#### Token缓存机制
- Token自动缓存到数据库，避免频繁获取
- 支持Token过期检测和自动刷新
- 程序重启时自动加载缓存的Token

#### 网络重试策略
- 指数退避重试：2秒 → 4秒 → 8秒 → 16秒 → 32秒
- 超时错误额外重试5次
- 网络状态检测，等待网络恢复

#### 数据库连接池
- 连接池大小：5个连接，最大溢出10个
- 自动健康检查，每5分钟检查一次
- 断线自动重连，连接失效自动清理

#### 故障恢复
- MySQL连接失败自动切换到文件存储
- 网络异常自动等待恢复
- 完善的异常处理，确保程序稳定运行

### 调试方法

1. 启用详细日志：`python main.py --verbose`
2. 执行单次检查：`python main.py --once`
3. 测试钉钉连接：`python main.py --test`
4. 启用调试模式：`python main.py --debug`
5. 查看统计信息：`python main.py --stats`

## 开发说明

### 代码规范
- 遵循PEP 8编码规范
- 使用类型注解提高代码可读性
- 添加详细的文档字符串

### 测试
```bash
# 运行单元测试
pytest tests/

# 运行测试并生成覆盖率报告
pytest --cov=src tests/
```

### 扩展开发
- 添加新的号码识别规则：修改 `number_analyzer.py`
- 添加新的通知渠道：参考 `dingtalk_notifier.py` 实现
- 修改数据存储格式：修改 `data_storage.py`

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 作者

- **alxxxxla** - 项目维护者

## 更新日志

### v2.0.0 (2025-07-04) - 企业级优化版本
- 🚀 **Token缓存机制**: 支持数据库缓存，避免频繁获取Token
- 🔗 **连接池管理**: MySQL连接池，支持断线重连和健康检查
- 🌐 **网络重试优化**: 指数退避策略，网络状态检测
- 🛡️ **错误处理增强**: 完善的异常处理和自动恢复机制
- 💾 **存储优化**: MySQL + 文件存储双重保障
- 🔧 **代码重构**: 模块化设计，提高可维护性
- 📊 **监控增强**: 更详细的统计信息和健康检查

### v1.0.0 (2024-07-03)
- 初始版本发布
- 实现基本的号码监控和识别功能
- 支持钉钉通知
- 完整的数据存储和管理功能
