@echo off
REM 联通手机号监控系统运行脚本 (Windows)
REM Author: alxxxxla

setlocal enabledelayedexpansion

REM 颜色定义（Windows不支持颜色，使用简单文本）
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

REM 检查Python环境
:check_python
python --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Python 未安装，请先安装Python 3.8+
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo %INFO% 检测到Python版本: %python_version%
goto :eof

REM 安装依赖
:install_dependencies
echo %INFO% 安装Python依赖包...

if exist requirements.txt (
    python -m pip install -r requirements.txt
    echo %SUCCESS% 依赖包安装完成
) else (
    echo %ERROR% requirements.txt 文件不存在
    exit /b 1
)
goto :eof

REM 检查配置文件
:check_config
if not exist config.env (
    if exist config.env.template (
        echo %WARNING% 配置文件不存在，从模板创建...
        copy config.env.template config.env
        echo %WARNING% 请编辑 config.env 文件，配置钉钉机器人信息
        echo %INFO% 配置完成后重新运行此脚本
        exit /b 0
    ) else (
        echo %ERROR% 配置文件模板不存在
        exit /b 1
    )
)
echo %SUCCESS% 配置文件检查通过
goto :eof

REM 创建必要目录
:create_directories
echo %INFO% 创建必要目录...
if not exist data mkdir data
if not exist logs mkdir logs
echo %SUCCESS% 目录创建完成
goto :eof

REM 运行测试
:run_tests
echo %INFO% 运行单元测试...

python -m pytest --version >nul 2>&1
if errorlevel 1 (
    echo %WARNING% pytest 未安装，跳过测试
) else (
    python -m pytest tests/ -v
    echo %SUCCESS% 测试完成
)
goto :eof

REM 显示帮助信息
:show_help
echo 联通手机号监控系统运行脚本
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   setup     - 初始化环境（安装依赖、创建配置等）
echo   start     - 启动监控系统
echo   once      - 执行单次检查
echo   test      - 测试钉钉连接
echo   stats     - 显示统计信息
echo   export    - 导出数据
echo   run-tests - 运行单元测试
echo   help      - 显示此帮助信息
echo.
echo 示例:
echo   %~nx0 setup          # 初始化环境
echo   %~nx0 start          # 启动监控
echo   %~nx0 once           # 单次检查
echo   %~nx0 test           # 测试连接
goto :eof

REM 主函数
:main
if "%1"=="" goto show_help
if "%1"=="help" goto show_help
if "%1"=="--help" goto show_help
if "%1"=="-h" goto show_help

if "%1"=="setup" (
    echo %INFO% 开始初始化环境...
    call :check_python
    call :install_dependencies
    call :check_config
    call :create_directories
    call :run_tests
    echo %SUCCESS% 环境初始化完成！
    echo %INFO% 请编辑 config.env 文件配置钉钉机器人信息，然后运行: %~nx0 start
    goto :eof
)

if "%1"=="start" (
    echo %INFO% 启动联通手机号监控系统...
    call :check_config
    python main.py
    goto :eof
)

if "%1"=="once" (
    echo %INFO% 执行单次检查...
    call :check_config
    python main.py --once
    goto :eof
)

if "%1"=="test" (
    echo %INFO% 测试钉钉连接...
    call :check_config
    python main.py --test
    goto :eof
)

if "%1"=="stats" (
    echo %INFO% 显示统计信息...
    python main.py --stats
    goto :eof
)

if "%1"=="export" (
    echo %INFO% 导出数据...
    python main.py --export
    goto :eof
)

if "%1"=="run-tests" (
    call :run_tests
    goto :eof
)

echo %ERROR% 未知选项: %1
call :show_help
exit /b 1

REM 执行主函数
call :main %*
