{% extends "base.html" %}

{% block title %}监控面板 - 联通手机号监控系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        监控控制面板
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-success" id="start-monitor-btn" onclick="startMonitor()">
                <i class="fas fa-play me-1"></i>启动监控
            </button>
            <button type="button" class="btn btn-danger" id="stop-monitor-btn" onclick="stopMonitor()">
                <i class="fas fa-stop me-1"></i>停止监控
            </button>
        </div>
        <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
    </div>
</div>

<!-- 系统状态卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">监控状态</div>
                        <div class="h5 mb-0 font-weight-bold" id="monitor-status">
                            {% if system_status.running %}运行中{% else %}已停止{% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-power-off fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card-blue">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">当前城市</div>
                        <div class="h5 mb-0 font-weight-bold" id="current-city">
                            {{ system_status.current_city_name or system_status.current_city }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-map-marker-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card-green">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">检查间隔</div>
                        <div class="h5 mb-0 font-weight-bold" id="check-interval">
                            {{ system_status.interval }}秒
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card-orange">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Token状态</div>
                        <div class="h5 mb-0 font-weight-bold" id="token-status">
                            {% if system_status.token_status == 'valid' %}有效{% elif system_status.token_status == 'expired' %}已过期{% else %}未知{% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-key fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计数据 -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    监控统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="border-end">
                            <h3 class="text-primary mb-1">{{ stats.total_processed or 0 }}</h3>
                            <p class="text-muted mb-0">总处理号码</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="border-end">
                            <h3 class="text-success mb-1">{{ stats.total_special or 0 }}</h3>
                            <p class="text-muted mb-0">发现特殊号码</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <h3 class="text-warning mb-1">
                            {% if stats.total_processed and stats.total_processed > 0 %}
                                {{ "%.2f"|format((stats.total_special or 0) / stats.total_processed * 100) }}%
                            {% else %}
                                0.00%
                            {% endif %}
                        </h3>
                        <p class="text-muted mb-0">发现率</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-database me-2"></i>
                    数据库状态
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="row">
                            <div class="col-12">
                                <span class="badge bg-{% if system_status.database_status == 'connected' %}success{% elif system_status.database_status == 'file_storage' %}warning{% else %}danger{% endif %} me-2">
                                    {% if system_status.database_status == 'connected' %}MySQL已连接{% elif system_status.database_status == 'file_storage' %}文件存储{% else %}连接错误{% endif %}
                                </span>
                            </div>
                        </div>
                        {% if system_status.token_expires_at %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">Token过期时间: {{ system_status.token_expires_at }}</small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近发现的特殊号码 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>
                    最近发现的特殊号码
                </h5>
                <span class="badge bg-primary" id="recent-count">{{ recent_numbers|length }}</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>手机号码</th>
                                <th>类型</th>
                                <th>模式</th>
                                <th>城市</th>
                                <th>发现时间</th>
                                <th>描述</th>
                            </tr>
                        </thead>
                        <tbody id="recent-numbers-table">
                            {% for number in recent_numbers %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ number.number }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ number.type }}</span>
                                </td>
                                <td>
                                    <code>{{ number.pattern }}</code>
                                </td>
                                <td>
                                    {% if number.city_name %}
                                        <span class="badge bg-secondary">{{ number.city_name }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ number.discovered_time.strftime('%m-%d %H:%M') if number.discovered_time else '-' }}</small>
                                </td>
                                <td>
                                    <small>{{ number.description }}</small>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center text-muted">
                                    <i class="fas fa-inbox me-2"></i>
                                    暂无数据
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 启动监控
    async function startMonitor() {
        const btn = document.getElementById('start-monitor-btn');
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>启动中...';
        
        const result = await apiRequest('/api/monitor/start', { method: 'POST' });
        
        if (result.success) {
            showNotification('监控已启动', 'success');
        } else {
            showNotification(result.message, 'danger');
        }
        
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-play me-1"></i>启动监控';
    }
    
    // 停止监控
    async function stopMonitor() {
        const btn = document.getElementById('stop-monitor-btn');
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>停止中...';
        
        const result = await apiRequest('/api/monitor/stop', { method: 'POST' });
        
        if (result.success) {
            showNotification('监控已停止', 'warning');
        } else {
            showNotification(result.message, 'danger');
        }
        
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-stop me-1"></i>停止监控';
    }
    
    // 刷新数据
    async function refreshData() {
        const result = await apiRequest('/api/monitor/status');
        if (result) {
            updateSystemStatus(result);
        }
        
        // 刷新页面数据
        location.reload();
    }
    
    // 监听新的特殊号码
    socket.on('new_special_numbers', function(numbers) {
        if (numbers && numbers.length > 0) {
            showNotification(`发现 ${numbers.length} 个新的特殊号码！`, 'success');
            
            // 更新表格
            const tbody = document.getElementById('recent-numbers-table');
            numbers.forEach(number => {
                const row = document.createElement('tr');
                row.className = 'table-success';
                row.innerHTML = `
                    <td><strong class="text-primary">${number.number}</strong></td>
                    <td><span class="badge bg-info">${number.type}</span></td>
                    <td><code>${number.pattern}</code></td>
                    <td>${number.city_name ? `<span class="badge bg-secondary">${number.city_name}</span>` : '<span class="text-muted">-</span>'}</td>
                    <td><small class="text-muted">${new Date(number.discovered_time).toLocaleString('zh-CN')}</small></td>
                    <td><small>${number.description}</small></td>
                `;
                tbody.insertBefore(row, tbody.firstChild);
            });
            
            // 更新计数
            const countBadge = document.getElementById('recent-count');
            const currentCount = parseInt(countBadge.textContent) || 0;
            countBadge.textContent = currentCount + numbers.length;
        }
    });
    
    // 更新系统状态显示
    function updateSystemStatus(status) {
        // 更新监控状态
        document.getElementById('monitor-status').textContent = status.running ? '运行中' : '已停止';
        
        // 更新当前城市
        document.getElementById('current-city').textContent = status.current_city_name || status.current_city;
        
        // 更新检查间隔
        document.getElementById('check-interval').textContent = status.interval + '秒';
        
        // 更新Token状态
        let tokenStatusText = '未知';
        if (status.token_status === 'valid') tokenStatusText = '有效';
        else if (status.token_status === 'expired') tokenStatusText = '已过期';
        document.getElementById('token-status').textContent = tokenStatusText;
        
        // 更新按钮状态
        const startBtn = document.getElementById('start-monitor-btn');
        const stopBtn = document.getElementById('stop-monitor-btn');
        
        if (status.running) {
            startBtn.disabled = true;
            stopBtn.disabled = false;
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
        }
    }
    
    // 页面加载时请求状态更新
    document.addEventListener('DOMContentLoaded', function() {
        socket.emit('request_status');
    });
</script>
{% endblock %}
