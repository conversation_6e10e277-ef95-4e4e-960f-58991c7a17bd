{% extends "base.html" %}

{% block title %}监控面板 - 联通手机号监控系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        监控控制面板
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-success" id="start-monitor-btn" onclick="startMonitor()">
                <i class="fas fa-play me-1"></i>启动监控
            </button>
            <button type="button" class="btn btn-danger" id="stop-monitor-btn" onclick="stopMonitor()">
                <i class="fas fa-stop me-1"></i>停止监控
            </button>
        </div>
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-city me-1"></i>监控城市
            </button>
            <ul class="dropdown-menu" id="city-selector">
                <li><h6 class="dropdown-header">选择监控城市</h6></li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <a class="dropdown-item" href="#" onclick="selectAllCities()">
                        <i class="fas fa-check-double me-2"></i>全部城市
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <!-- 城市选项将通过JavaScript动态加载 -->
            </ul>
        </div>
        <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
    </div>
</div>

<!-- 系统状态卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">监控状态</div>
                        <div class="h5 mb-0 font-weight-bold" id="monitor-status">
                            {% if system_status.running %}运行中{% else %}已停止{% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-power-off fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card-blue">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">当前城市</div>
                        <div class="h5 mb-0 font-weight-bold" id="current-city">
                            {{ system_status.current_city_name or system_status.current_city }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-map-marker-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card-green">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">检查间隔</div>
                        <div class="h5 mb-0 font-weight-bold" id="check-interval">
                            {{ system_status.interval }}秒
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card-orange">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Token状态</div>
                        <div class="h5 mb-0 font-weight-bold" id="token-status">
                            {% if system_status.token_status == 'valid' %}有效{% elif system_status.token_status == 'expired' %}已过期{% else %}未知{% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-key fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 实时监控统计 -->
<div class="row mb-4">
    <div class="col-lg-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    本轮监控统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-info mb-1" id="current-processed">0</h4>
                        <small class="text-muted">本轮处理</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1" id="current-special">0</h4>
                        <small class="text-muted">本轮发现</small>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">上次检查: <span id="last-check-time">-</span></small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    总体监控统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="border-end">
                            <h3 class="text-primary mb-1" id="total-processed">{{ stats.total_processed or 0 }}</h3>
                            <p class="text-muted mb-0">总处理号码</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="border-end">
                            <h3 class="text-success mb-1" id="total-special">{{ stats.total_special or 0 }}</h3>
                            <p class="text-muted mb-0">发现特殊号码</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <h3 class="text-warning mb-1" id="discovery-rate">
                            {% if stats.total_processed and stats.total_processed > 0 %}
                                {{ "%.2f"|format((stats.total_special or 0) / stats.total_processed * 100) }}%
                            {% else %}
                                0.00%
                            {% endif %}
                        </h3>
                        <p class="text-muted mb-0">发现率</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 监控城市状态 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-city me-2"></i>
                    监控城市状态
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="city-status-container">
                    <!-- 城市状态将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-database me-2"></i>
                    数据库状态
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="row">
                            <div class="col-12">
                                <span class="badge bg-{% if system_status.database_status == 'connected' %}success{% elif system_status.database_status == 'file_storage' %}warning{% else %}danger{% endif %} me-2">
                                    {% if system_status.database_status == 'connected' %}MySQL已连接{% elif system_status.database_status == 'file_storage' %}文件存储{% else %}连接错误{% endif %}
                                </span>
                            </div>
                        </div>
                        {% if system_status.token_expires_at %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">Token过期时间: {{ system_status.token_expires_at }}</small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近发现的特殊号码 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>
                    最近发现的特殊号码
                </h5>
                <span class="badge bg-primary" id="recent-count">{{ recent_numbers|length }}</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>手机号码</th>
                                <th>类型</th>
                                <th>模式</th>
                                <th>城市</th>
                                <th>发现时间</th>
                                <th>描述</th>
                            </tr>
                        </thead>
                        <tbody id="recent-numbers-table">
                            {% for number in recent_numbers %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ number.number }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ number.type }}</span>
                                </td>
                                <td>
                                    <code>{{ number.pattern }}</code>
                                </td>
                                <td>
                                    {% if number.city_name %}
                                        <span class="badge bg-secondary">{{ number.city_name }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ number.discovered_time.strftime('%m-%d %H:%M') if number.discovered_time else '-' }}</small>
                                </td>
                                <td>
                                    <small>{{ number.description }}</small>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center text-muted">
                                    <i class="fas fa-inbox me-2"></i>
                                    暂无数据
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 启动监控
    async function startMonitor() {
        const btn = document.getElementById('start-monitor-btn');
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>启动中...';
        
        const result = await apiRequest('/api/monitor/start', { method: 'POST' });
        
        if (result.success) {
            showNotification('监控已启动', 'success');
        } else {
            showNotification(result.message, 'danger');
        }
        
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-play me-1"></i>启动监控';
    }
    
    // 停止监控
    async function stopMonitor() {
        const btn = document.getElementById('stop-monitor-btn');
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>停止中...';
        
        const result = await apiRequest('/api/monitor/stop', { method: 'POST' });
        
        if (result.success) {
            showNotification('监控已停止', 'warning');
        } else {
            showNotification(result.message, 'danger');
        }
        
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-stop me-1"></i>停止监控';
    }
    
    // 刷新数据
    async function refreshData() {
        const result = await apiRequest('/api/monitor/status');
        if (result) {
            updateSystemStatus(result);
        }
        
        // 刷新页面数据
        location.reload();
    }
    
    // 监听新的特殊号码
    socket.on('new_special_numbers', function(numbers) {
        if (numbers && numbers.length > 0) {
            showNotification(`发现 ${numbers.length} 个新的特殊号码！`, 'success');

            // 更新本轮统计
            const currentSpecial = document.getElementById('current-special');
            const currentCount = parseInt(currentSpecial.textContent) || 0;
            currentSpecial.textContent = currentCount + numbers.length;

            // 更新表格
            const tbody = document.getElementById('recent-numbers-table');
            numbers.forEach(number => {
                const row = document.createElement('tr');
                row.className = 'table-success';
                row.innerHTML = `
                    <td><strong class="text-primary">${number.number}</strong></td>
                    <td><span class="badge bg-info">${number.type}</span></td>
                    <td><code>${number.pattern}</code></td>
                    <td>${number.city_name ? `<span class="badge bg-secondary">${number.city_name}</span>` : '<span class="text-muted">-</span>'}</td>
                    <td><small class="text-muted">${new Date(number.discovered_time).toLocaleString('zh-CN')}</small></td>
                    <td><small>${number.description}</small></td>
                `;
                tbody.insertBefore(row, tbody.firstChild);
            });

            // 更新计数
            const countBadge = document.getElementById('recent-count');
            const currentTableCount = parseInt(countBadge.textContent) || 0;
            countBadge.textContent = currentTableCount + numbers.length;
        }
    });

    // 监听监控统计更新
    socket.on('monitor_stats', function(stats) {
        // 更新本轮统计
        if (stats.current_processed !== undefined) {
            document.getElementById('current-processed').textContent = stats.current_processed;
        }
        if (stats.current_special !== undefined) {
            document.getElementById('current-special').textContent = stats.current_special;
        }
        if (stats.last_check_time) {
            document.getElementById('last-check-time').textContent = new Date(stats.last_check_time).toLocaleString('zh-CN');
        }

        // 更新总体统计
        if (stats.total_processed !== undefined) {
            document.getElementById('total-processed').textContent = stats.total_processed;
        }
        if (stats.total_special !== undefined) {
            document.getElementById('total-special').textContent = stats.total_special;
        }
        if (stats.discovery_rate !== undefined) {
            document.getElementById('discovery-rate').textContent = stats.discovery_rate + '%';
        }
    });
    
    // 更新系统状态显示
    function updateSystemStatus(status) {
        // 更新监控状态
        document.getElementById('monitor-status').textContent = status.running ? '运行中' : '已停止';
        
        // 更新当前城市
        document.getElementById('current-city').textContent = status.current_city_name || status.current_city;
        
        // 更新检查间隔
        document.getElementById('check-interval').textContent = status.interval + '秒';
        
        // 更新Token状态
        let tokenStatusText = '未知';
        if (status.token_status === 'valid') tokenStatusText = '有效';
        else if (status.token_status === 'expired') tokenStatusText = '已过期';
        document.getElementById('token-status').textContent = tokenStatusText;
        
        // 更新按钮状态
        const startBtn = document.getElementById('start-monitor-btn');
        const stopBtn = document.getElementById('stop-monitor-btn');
        
        if (status.running) {
            startBtn.disabled = true;
            stopBtn.disabled = false;
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
        }
    }
    
    // 加载城市列表
    async function loadCities() {
        try {
            const result = await apiRequest('/api/cities');
            if (result.success) {
                const cities = result.data;
                const citySelector = document.getElementById('city-selector');
                const cityStatusContainer = document.getElementById('city-status-container');

                // 清空现有内容
                const existingItems = citySelector.querySelectorAll('.city-item');
                existingItems.forEach(item => item.remove());
                cityStatusContainer.innerHTML = '';

                // 添加城市选项
                Object.entries(cities).forEach(([cityCode, cityInfo]) => {
                    // 添加到下拉菜单
                    const li = document.createElement('li');
                    li.className = 'city-item';
                    li.innerHTML = `
                        <a class="dropdown-item" href="#" onclick="toggleCityMonitor('${cityCode}')">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            ${cityInfo.city_name} (${cityCode})
                            <span class="badge bg-primary ms-2">${cityInfo.enabled_rules.length}规则</span>
                        </a>
                    `;
                    citySelector.appendChild(li);

                    // 添加到状态显示
                    const col = document.createElement('div');
                    col.className = 'col-md-6 col-lg-4 mb-3';
                    col.innerHTML = `
                        <div class="card border-start border-primary border-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">${cityInfo.city_name}</h6>
                                        <small class="text-muted">${cityInfo.area_code} | ${cityCode}</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-success" id="city-status-${cityCode}">监控中</span>
                                        <div class="mt-1">
                                            <small class="text-muted" id="city-count-${cityCode}">0个号码</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    cityStatusContainer.appendChild(col);
                });
            }
        } catch (error) {
            console.error('加载城市列表失败:', error);
        }
    }

    // 选择所有城市
    function selectAllCities() {
        showNotification('已选择监控所有城市', 'info');
    }

    // 切换城市监控
    function toggleCityMonitor(cityCode) {
        showNotification(`切换城市 ${cityCode} 的监控状态`, 'info');
    }

    // 更新城市状态
    function updateCityStatus(cityCode, status, count) {
        const statusElement = document.getElementById(`city-status-${cityCode}`);
        const countElement = document.getElementById(`city-count-${cityCode}`);

        if (statusElement) {
            statusElement.className = status === 'active' ? 'badge bg-success' : 'badge bg-secondary';
            statusElement.textContent = status === 'active' ? '监控中' : '暂停';
        }

        if (countElement) {
            countElement.textContent = `${count}个号码`;
        }
    }

    // 页面加载时请求状态更新
    document.addEventListener('DOMContentLoaded', function() {
        socket.emit('request_status');
        loadCities();

        // 定期更新本轮统计的时间
        setInterval(() => {
            const lastCheckElement = document.getElementById('last-check-time');
            if (lastCheckElement && lastCheckElement.textContent !== '-') {
                // 可以在这里添加时间更新逻辑
            }
        }, 1000);
    });
</script>
{% endblock %}
