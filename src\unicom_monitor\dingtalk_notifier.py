"""
钉钉通知模块

负责通过钉钉机器人发送特殊号码发现通知。
"""

import json
import time
import hmac
import base64
import hashlib
import urllib.parse
import logging
from typing import List, Optional
import requests

from .config import Config
from .number_analyzer import SpecialNumber
from .network_utils import NetworkRetryMixin


logger = logging.getLogger(__name__)


class DingTalkNotifier(NetworkRetryMixin):
    """钉钉通知器"""
    
    def __init__(self, config: Config) -> None:
        """
        初始化钉钉通知器

        Args:
            config: 配置对象
        """
        super().__init__()
        self.config = config
        self.webhook_url = config.dingtalk_webhook
        self.secret = config.dingtalk_secret
        self.session = requests.Session()
        
        # 检查配置
        if not self.webhook_url:
            logger.warning("钉钉webhook URL未配置，通知功能将被禁用")
        if not self.secret:
            logger.warning("钉钉secret未配置，将使用无签名模式")
    
    def _generate_sign(self, timestamp: int) -> str:
        """
        生成钉钉签名
        
        Args:
            timestamp: 时间戳（毫秒）
            
        Returns:
            签名字符串
        """
        if not self.secret:
            return ""
        
        string_to_sign = f"{timestamp}\n{self.secret}"
        hmac_code = hmac.new(
            self.secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return sign
    
    def _build_webhook_url(self) -> str:
        """
        构建带签名的webhook URL
        
        Returns:
            完整的webhook URL
        """
        if not self.secret:
            return self.webhook_url
        
        timestamp = int(round(time.time() * 1000))
        sign = self._generate_sign(timestamp)
        
        separator = "&" if "?" in self.webhook_url else "?"
        return f"{self.webhook_url}{separator}timestamp={timestamp}&sign={sign}"
    
    def _format_message(self, special_numbers: List[SpecialNumber]) -> str:
        """
        格式化通知消息
        
        Args:
            special_numbers: 特殊号码列表
            
        Returns:
            格式化的消息文本
        """
        if not special_numbers:
            return "没有发现新的特殊号码"
        
        message_lines = [
            "🎉 发现新的特殊手机号码！",
            "",
            f"📊 本次发现 {len(special_numbers)} 个特殊号码：",
            ""
        ]
        
        # 按类型分组
        type_groups = {}
        for special in special_numbers:
            type_name = special.number_type.value
            if type_name not in type_groups:
                type_groups[type_name] = []
            type_groups[type_name].append(special)
        
        # 生成消息内容
        for type_name, numbers in type_groups.items():
            message_lines.append(f"📱 {type_name} ({len(numbers)}个):")
            for special in numbers:
                message_lines.append(f"  • {special.number} - {special.pattern}")
            message_lines.append("")
        
        # 添加时间戳
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        message_lines.extend([
            "---",
            f"⏰ 发现时间: {current_time}",
            "🤖 联通号码监控系统 by alxxxxla"
        ])
        
        return "\n".join(message_lines)
    
    def _send_text_message(self, content: str) -> bool:
        """
        发送文本消息

        Args:
            content: 消息内容

        Returns:
            发送是否成功
        """
        if not self.webhook_url:
            logger.warning("钉钉webhook URL未配置，跳过发送")
            return False

        def _send_request():
            url = self._build_webhook_url()

            payload = {
                "msgtype": "text",
                "text": {
                    "content": content
                }
            }

            headers = {
                "Content-Type": "application/json"
            }

            response = self.session.post(
                url,
                json=payload,
                headers=headers,
                timeout=10
            )
            response.raise_for_status()

            result = response.json()
            if result.get("errcode") == 0:
                logger.info("钉钉消息发送成功")
                return True
            else:
                error_msg = f"钉钉消息发送失败: {result}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        try:
            return self.retry_with_backoff(_send_request, "钉钉消息发送")
        except Exception as e:
            logger.error(f"钉钉消息发送最终失败: {e}")
            return False
    
    def _send_markdown_message(self, title: str, content: str) -> bool:
        """
        发送Markdown消息

        Args:
            title: 消息标题
            content: Markdown内容

        Returns:
            发送是否成功
        """
        if not self.webhook_url:
            logger.warning("钉钉webhook URL未配置，跳过发送")
            return False

        def _send_request():
            url = self._build_webhook_url()

            payload = {
                "msgtype": "markdown",
                "markdown": {
                    "title": title,
                    "text": content
                }
            }

            headers = {
                "Content-Type": "application/json"
            }

            response = self.session.post(
                url,
                json=payload,
                headers=headers,
                timeout=10
            )
            response.raise_for_status()

            result = response.json()
            if result.get("errcode") == 0:
                logger.info("钉钉Markdown消息发送成功")
                return True
            else:
                error_msg = f"钉钉Markdown消息发送失败: {result}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        try:
            return self.retry_with_backoff(_send_request, "钉钉Markdown消息发送")
        except Exception as e:
            logger.error(f"钉钉Markdown消息发送最终失败: {e}")
            return False
    
    def notify_special_numbers(self, special_numbers: List[SpecialNumber]) -> bool:
        """
        通知发现的特殊号码
        
        Args:
            special_numbers: 特殊号码列表
            
        Returns:
            通知是否成功
        """
        if not special_numbers:
            logger.info("没有特殊号码需要通知")
            return True
        
        message = self._format_message(special_numbers)
        logger.info(f"准备发送钉钉通知，包含 {len(special_numbers)} 个特殊号码")
        
        return self._send_text_message(message)
    
    def send_status_report(self, total_processed: int, total_special: int, 
                          type_distribution: dict) -> bool:
        """
        发送状态报告
        
        Args:
            total_processed: 总处理数量
            total_special: 特殊号码总数
            type_distribution: 类型分布
            
        Returns:
            发送是否成功
        """
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        
        content_lines = [
            "📊 联通号码监控系统状态报告",
            "",
            f"📈 总处理号码: {total_processed}",
            f"🎯 发现特殊号码: {total_special}",
            "",
            "📱 特殊号码类型分布:"
        ]
        
        for type_name, count in type_distribution.items():
            content_lines.append(f"  • {type_name}: {count}个")
        
        content_lines.extend([
            "",
            f"⏰ 报告时间: {current_time}",
            "🤖 联通号码监控系统 by alxxxxla"
        ])
        
        content = "\n".join(content_lines)
        return self._send_text_message(content)
    
    def test_connection(self) -> bool:
        """
        测试钉钉连接
        
        Returns:
            连接是否正常
        """
        test_message = f"🔧 钉钉通知测试\n\n⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n🤖 联通号码监控系统 by alxxxxla"
        return self._send_text_message(test_message)
