#!/bin/bash

# 联通手机号监控系统运行脚本
# Author: alxxxxla

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装，请先安装Python 3.8+"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    print_info "检测到Python版本: $python_version"
    
    if [[ $(echo "$python_version >= 3.8" | bc -l) -eq 0 ]]; then
        print_error "需要Python 3.8或更高版本"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    print_info "安装Python依赖包..."
    
    if [ -f "requirements.txt" ]; then
        python3 -m pip install -r requirements.txt
        print_success "依赖包安装完成"
    else
        print_error "requirements.txt 文件不存在"
        exit 1
    fi
}

# 检查配置文件
check_config() {
    if [ ! -f "config.env" ]; then
        if [ -f "config.env.template" ]; then
            print_warning "配置文件不存在，从模板创建..."
            cp config.env.template config.env
            print_warning "请编辑 config.env 文件，配置钉钉机器人信息"
            print_info "配置完成后重新运行此脚本"
            exit 0
        else
            print_error "配置文件模板不存在"
            exit 1
        fi
    fi
    print_success "配置文件检查通过"
}

# 创建必要目录
create_directories() {
    print_info "创建必要目录..."
    mkdir -p data logs
    print_success "目录创建完成"
}

# 运行测试
run_tests() {
    print_info "运行单元测试..."
    
    if command -v pytest &> /dev/null; then
        python3 -m pytest tests/ -v
        print_success "测试完成"
    else
        print_warning "pytest 未安装，跳过测试"
    fi
}

# 显示帮助信息
show_help() {
    echo "联通手机号监控系统运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  setup     - 初始化环境（安装依赖、创建配置等）"
    echo "  start     - 启动监控系统"
    echo "  once      - 执行单次检查"
    echo "  test      - 测试钉钉连接"
    echo "  stats     - 显示统计信息"
    echo "  export    - 导出数据"
    echo "  run-tests - 运行单元测试"
    echo "  help      - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 setup          # 初始化环境"
    echo "  $0 start          # 启动监控"
    echo "  $0 once           # 单次检查"
    echo "  $0 test           # 测试连接"
}

# 主函数
main() {
    case "${1:-help}" in
        setup)
            print_info "开始初始化环境..."
            check_python
            install_dependencies
            check_config
            create_directories
            run_tests
            print_success "环境初始化完成！"
            print_info "请编辑 config.env 文件配置钉钉机器人信息，然后运行: $0 start"
            ;;
        start)
            print_info "启动联通手机号监控系统..."
            check_config
            python3 main.py
            ;;
        once)
            print_info "执行单次检查..."
            check_config
            python3 main.py --once
            ;;
        test)
            print_info "测试钉钉连接..."
            check_config
            python3 main.py --test
            ;;
        stats)
            print_info "显示统计信息..."
            python3 main.py --stats
            ;;
        export)
            print_info "导出数据..."
            python3 main.py --export
            ;;
        run-tests)
            run_tests
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
