"""
主监控程序

整合所有模块，实现持续监控联通手机号API并处理特殊号码。
"""

import time
import signal
import logging
import threading
from typing import Optional
from datetime import datetime, timedelta

from .config import Config
from .api_client import UnicomAPIClient
from .number_analyzer import NumberAnalyzer
from .data_storage import DataStorage
from .dingtalk_notifier import DingTalkNotifier
from .db_manager import DatabaseManager


logger = logging.getLogger(__name__)


class PhoneNumberMonitor:
    """手机号码监控器"""
    
    def __init__(self, config_file: str = "config.env") -> None:
        """
        初始化监控器
        
        Args:
            config_file: 配置文件路径
        """
        logger.info(f"开始初始化监控器，配置文件: {config_file}")

        try:
            logger.info("正在加载配置...")
            self.config = Config(config_file)
            logger.info("配置加载成功")

            # 优先初始化日志系统，以便捕获后续初始化过程中的任何错误
            self._setup_logging()

            logger.info("正在创建数据存储...")
            self.storage = DataStorage(self.config)
            logger.info("数据存储创建成功")

            logger.info("正在创建API客户端...")
            self.api_client = UnicomAPIClient(self.config, self.storage.db_manager)
            logger.info("API客户端创建成功")

            logger.info("正在创建号码分析器...")
            self.analyzer = NumberAnalyzer()
            logger.info("号码分析器创建成功")

            logger.info("正在创建钉钉通知器...")
            self.notifier = DingTalkNotifier(self.config)
            logger.info("钉钉通知器创建成功")

        except Exception as e:
            logger.error(f"初始化监控器时发生错误: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise
        
        self.running = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.last_report_time = datetime.now()
        self.report_interval = timedelta(hours=6)  # 每6小时发送一次状态报告
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _setup_logging(self) -> None:
        """设置日志配置"""
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config.log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def _signal_handler(self, signum: int, frame) -> None:
        """信号处理器"""
        logger.info(f"接收到信号 {signum}，准备停止监控...")
        self.stop()
    
    def _should_send_report(self) -> bool:
        """检查是否应该发送状态报告"""
        return datetime.now() - self.last_report_time >= self.report_interval
    
    def _send_status_report(self) -> None:
        """发送状态报告"""
        try:
            stats = self.storage.get_statistics()
            success = self.notifier.send_status_report(
                total_processed=stats['total_processed'],
                total_special=stats['total_special'],
                type_distribution=stats['type_distribution']
            )
            
            if success:
                self.last_report_time = datetime.now()
                logger.info("状态报告发送成功")
            else:
                logger.warning("状态报告发送失败")
                
        except Exception as e:
            logger.error(f"发送状态报告时发生错误: {e}")
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        logger.info("开始监控循环")
        
        # 在新线程的开始处再次进行初始化，确保异常能被捕获
        try:
            if not self.api_client.unicom_token:
                if not self.api_client._initialize_token():
                    logger.error("无法启动监控，因为首次Token获取失败")
                    self.running = False # 关键：设置状态以终止线程
                    return
        except Exception as e:
            logger.error(f"监控线程初始化失败（API Token）: {e}")
            self.running = False
            return

        while self.running:
            try:
                # 获取手机号码
                logger.info("正在获取手机号码...")
                phone_numbers = self.api_client.get_numbers()
                
                if not phone_numbers:
                    logger.warning("未获取到任何手机号码")
                else:
                    logger.info(f"获取到 {len(phone_numbers)} 个手机号码")
                    
                    # 过滤新号码
                    new_numbers = [
                        num for num in phone_numbers 
                        if not self.storage.is_number_processed(num)
                    ]
                    
                    if new_numbers:
                        logger.info(f"发现 {len(new_numbers)} 个新号码")
                        
                        # 分析特殊号码
                        special_numbers = self.analyzer.analyze_numbers(new_numbers)
                        
                        # 保存新发现的特殊号码
                        new_special_numbers = self.storage.save_special_numbers(special_numbers)
                        
                        # 标记号码为已处理
                        self.storage.mark_numbers_processed(new_numbers)
                        
                        # 发送通知
                        if new_special_numbers:
                            logger.info(f"发现 {len(new_special_numbers)} 个新的特殊号码，准备发送通知")
                            success = self.notifier.notify_special_numbers(new_special_numbers)
                            if not success:
                                logger.warning("钉钉通知发送失败")
                        else:
                            logger.info("没有新的特殊号码")
                    else:
                        logger.info("没有新号码需要处理")
                
                # 检查是否需要发送状态报告
                if self._should_send_report():
                    self._send_status_report()
                
                # 等待下次检查
                if self.running:
                    logger.info(f"等待 {self.config.request_interval} 秒后进行下次检查...")
                    time.sleep(self.config.request_interval)
                    
            except KeyboardInterrupt:
                logger.info("接收到键盘中断，停止监控")
                break
            except Exception as e:
                logger.error(f"监控循环中发生错误: {e}")
                if self.running:
                    logger.info(f"等待 {self.config.retry_delay} 秒后重试...")
                    time.sleep(self.config.retry_delay)
        
        logger.info("监控循环结束")
    
    def start(self) -> None:
        """启动监控"""
        if self.running:
            logger.warning("监控已在运行中")
            return
        
        logger.info("启动联通手机号监控系统")
        logger.info(f"配置信息:")
        logger.info(f"  - 请求间隔: {self.config.request_interval} 秒")
        logger.info(f"  - 最大重试次数: {self.config.max_retries}")
        logger.info(f"  - 数据存储: MySQL ({self.config.db_host}:{self.config.db_port})")
        logger.info(f"  - 钉钉通知: {'已配置' if self.config.dingtalk_webhook else '未配置'}")
        
        # 测试钉钉连接
        if self.config.dingtalk_webhook:
            logger.info("测试钉钉连接...")
            if self.notifier.test_connection():
                logger.info("钉钉连接测试成功")
            else:
                logger.warning("钉钉连接测试失败，但监控将继续运行")
        
        # 显示统计信息
        stats = self.storage.get_statistics()
        logger.info(f"历史统计: 已处理 {stats['total_processed']} 个号码，发现 {stats['total_special']} 个特殊号码")
        
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("监控系统已启动")
    
    def stop(self) -> None:
        """停止监控"""
        if not self.running:
            logger.warning("监控未在运行")
            return
        
        logger.info("正在停止监控系统...")
        self.running = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        # 关闭资源
        self.api_client.close()
        self.storage.close()
        
        logger.info("监控系统已停止")
    
    def is_running(self) -> bool:
        """检查监控线程是否仍在运行"""
        return self.running and self.monitor_thread is not None and self.monitor_thread.is_alive()
    
    def run_once(self) -> None:
        """运行一次检查（用于测试）"""
        logger.info("执行单次检查...")
        
        try:
            # 获取手机号码
            phone_numbers = self.api_client.get_numbers()
            
            if not phone_numbers:
                logger.warning("未获取到任何手机号码")
                return
            
            logger.info(f"获取到 {len(phone_numbers)} 个手机号码")
            
            # 分析特殊号码
            special_numbers = self.analyzer.analyze_numbers(phone_numbers)
            
            if special_numbers:
                logger.info(f"发现 {len(special_numbers)} 个特殊号码:")
                for special in special_numbers:
                    logger.info(f"  {special.description}: {special.number}")
                
                # 保存结果
                new_special_numbers = self.storage.save_special_numbers(special_numbers)
                
                # 发送通知
                if new_special_numbers:
                    self.notifier.notify_special_numbers(new_special_numbers)
            else:
                logger.info("未发现特殊号码")
            
            # 标记号码为已处理
            self.storage.mark_numbers_processed(phone_numbers)
            
        except Exception as e:
            logger.error(f"单次检查时发生错误: {e}")
    
    def get_statistics(self) -> dict:
        """获取统计信息"""
        return self.storage.get_statistics()
    
    def export_data(self, output_file: str = None) -> str:
        """导出数据"""
        return self.storage.export_to_json(output_file)
