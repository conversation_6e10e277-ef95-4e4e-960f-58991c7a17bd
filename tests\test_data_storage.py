"""
数据存储测试模块
"""

import pytest
import json
import os
import tempfile
import sys
from pathlib import Path
from unittest.mock import Mock, patch

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from unicom_monitor.data_storage import DataStorage
from unicom_monitor.config import Config
from unicom_monitor.number_analyzer import SpecialNumber, NumberType


class TestDataStorage:
    """数据存储测试类"""
    
    def setup_method(self):
        """测试方法设置"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建模拟配置
        self.mock_config = Mock(spec=Config)
        self.mock_config.data_dir = self.temp_dir
        self.mock_config.history_file = "test_history.json"
        self.mock_config.results_file = "test_results.csv"
        
        self.storage = DataStorage(self.mock_config)
    
    def teardown_method(self):
        """测试方法清理"""
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_initial_empty_state(self):
        """测试初始空状态"""
        assert len(self.storage.processed_numbers) == 0
        assert len(self.storage.special_numbers_history) == 0
    
    def test_mark_numbers_processed(self):
        """测试标记号码为已处理"""
        numbers = ["18561859999", "18561851234", "18561859847"]
        
        self.storage.mark_numbers_processed(numbers)
        
        assert len(self.storage.processed_numbers) == 3
        for number in numbers:
            assert self.storage.is_number_processed(number)
    
    def test_is_number_processed(self):
        """测试检查号码是否已处理"""
        number = "18561859999"
        
        assert not self.storage.is_number_processed(number)
        
        self.storage.mark_numbers_processed([number])
        
        assert self.storage.is_number_processed(number)
    
    def test_save_special_numbers_new(self):
        """测试保存新的特殊号码"""
        special_numbers = [
            SpecialNumber(
                number="18561859999",
                number_type=NumberType.TAIL_3A,
                pattern="999",
                description="尾号3A: 999"
            ),
            SpecialNumber(
                number="18561851234",
                number_type=NumberType.TAIL_4_SEQUENCE,
                pattern="1234",
                description="尾号4顺子(递增): 1234"
            )
        ]
        
        new_numbers = self.storage.save_special_numbers(special_numbers)
        
        assert len(new_numbers) == 2
        assert len(self.storage.special_numbers_history) == 2
        
        # 验证CSV文件是否创建
        csv_path = os.path.join(self.temp_dir, "test_results.csv")
        assert os.path.exists(csv_path)
    
    def test_save_special_numbers_duplicate(self):
        """测试保存重复的特殊号码"""
        special_number = SpecialNumber(
            number="18561859999",
            number_type=NumberType.TAIL_3A,
            pattern="999",
            description="尾号3A: 999"
        )
        
        # 第一次保存
        new_numbers1 = self.storage.save_special_numbers([special_number])
        assert len(new_numbers1) == 1
        
        # 第二次保存相同号码
        new_numbers2 = self.storage.save_special_numbers([special_number])
        assert len(new_numbers2) == 0  # 应该没有新号码
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        # 添加一些测试数据
        self.storage.mark_numbers_processed(["18561859999", "18561851234"])
        
        special_numbers = [
            SpecialNumber("18561859999", NumberType.TAIL_3A, "999", "尾号3A: 999"),
            SpecialNumber("18561851234", NumberType.TAIL_4_SEQUENCE, "1234", "尾号4顺子: 1234")
        ]
        self.storage.save_special_numbers(special_numbers)
        
        stats = self.storage.get_statistics()
        
        assert stats['total_processed'] == 2
        assert stats['total_special'] == 2
        assert stats['type_distribution']['尾号3A'] == 1
        assert stats['type_distribution']['尾号4顺子'] == 1
        assert 'last_updated' in stats
    
    def test_export_to_json(self):
        """测试导出到JSON"""
        # 添加测试数据
        special_numbers = [
            SpecialNumber("18561859999", NumberType.TAIL_3A, "999", "尾号3A: 999")
        ]
        self.storage.save_special_numbers(special_numbers)
        
        # 导出数据
        export_file = self.storage.export_to_json()
        
        assert os.path.exists(export_file)
        
        # 验证导出内容
        with open(export_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        assert 'statistics' in data
        assert 'special_numbers' in data
        assert 'export_time' in data
        assert len(data['special_numbers']) == 1
    
    def test_clear_history(self):
        """测试清空历史记录"""
        # 添加测试数据
        self.storage.mark_numbers_processed(["18561859999"])
        special_numbers = [
            SpecialNumber("18561859999", NumberType.TAIL_3A, "999", "尾号3A: 999")
        ]
        self.storage.save_special_numbers(special_numbers)
        
        # 验证数据存在
        assert len(self.storage.processed_numbers) > 0
        assert len(self.storage.special_numbers_history) > 0
        
        # 清空历史
        self.storage.clear_history()
        
        # 验证数据已清空
        assert len(self.storage.processed_numbers) == 0
        assert len(self.storage.special_numbers_history) == 0
    
    def test_load_existing_history(self):
        """测试加载现有历史记录"""
        # 创建历史文件
        history_data = {
            'processed_numbers': ['18561859999', '18561851234'],
            'last_updated': '2024-07-03T12:00:00'
        }
        
        history_file = os.path.join(self.temp_dir, "test_history.json")
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(history_data, f)
        
        # 创建新的存储实例来测试加载
        new_storage = DataStorage(self.mock_config)
        
        assert len(new_storage.processed_numbers) == 2
        assert '18561859999' in new_storage.processed_numbers
        assert '18561851234' in new_storage.processed_numbers
    
    def test_load_existing_csv(self):
        """测试加载现有CSV文件"""
        # 创建CSV文件
        import pandas as pd
        
        csv_data = [
            {
                'number': '18561859999',
                'type': '尾号3A',
                'pattern': '999',
                'description': '尾号3A: 999',
                'discovered_time': '2024-07-03T12:00:00'
            }
        ]
        
        csv_file = os.path.join(self.temp_dir, "test_results.csv")
        df = pd.DataFrame(csv_data)
        df.to_csv(csv_file, index=False, encoding='utf-8')
        
        # 创建新的存储实例来测试加载
        new_storage = DataStorage(self.mock_config)
        
        assert len(new_storage.special_numbers_history) == 1
        assert new_storage.special_numbers_history[0]['number'] == '18561859999'


if __name__ == "__main__":
    pytest.main([__file__])
