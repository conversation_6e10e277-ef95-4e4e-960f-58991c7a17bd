2025-07-04 11:56:15,405 - root - INFO - 程序启动，开始解析参数...
2025-07-04 11:56:15,407 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 11:56:38,855 - root - INFO - 程序启动，开始解析参数...
2025-07-04 11:56:38,857 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 11:56:38,857 - root - INFO - 开始初始化 PhoneNumberMonitor...
2025-07-04 11:56:38,857 - root - INFO - 正在加载配置...
2025-07-04 11:56:38,860 - root - INFO - 配置加载完毕。
2025-07-04 11:56:38,860 - root - INFO - 日志系统配置完毕。
2025-07-04 11:56:38,860 - root - INFO - 正在初始化API客户端...
2025-07-04 11:56:38,860 - root - INFO - API客户端初始化完毕。
2025-07-04 11:56:38,862 - root - INFO - 正在初始化号码分析器...
2025-07-04 11:56:38,862 - root - INFO - 号码分析器初始化完毕。
2025-07-04 11:56:38,862 - root - INFO - 正在初始化数据存储...
2025-07-04 11:57:12,188 - root - INFO - 程序启动，开始解析参数...
2025-07-04 11:57:12,189 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 11:58:01,117 - root - INFO - 程序启动，开始解析参数...
2025-07-04 11:58:01,118 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:02:59,114 - root - INFO - 程序启动，开始解析参数...
2025-07-04 12:02:59,116 - root - INFO - 已启用调试模式 (DEBUG)
2025-07-04 12:02:59,116 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:02:59,116 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 12:02:59,116 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 12:02:59,119 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 12:02:59,119 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 12:02:59,119 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 12:02:59,119 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 12:02:59,119 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 12:02:59,119 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 12:02:59,120 - unicom_monitor.monitor - ERROR - 初始化监控器时发生错误: 'Config' object has no attribute 'data_dir'
2025-07-04 12:02:59,120 - unicom_monitor.monitor - ERROR - 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\10010\src\unicom_monitor\monitor.py", line 53, in __init__
    self.storage = DataStorage(self.config)
                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\10010\src\unicom_monitor\data_storage.py", line 36, in __init__
    self.history_file_path = os.path.join(config.data_dir, config.history_file)
                                          ^^^^^^^^^^^^^^^
AttributeError: 'Config' object has no attribute 'data_dir'

2025-07-04 12:02:59,121 - root - ERROR - 程序异常退出
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\10010\main.py", line 109, in main
    monitor = PhoneNumberMonitor(args.config)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\10010\src\unicom_monitor\monitor.py", line 53, in __init__
    self.storage = DataStorage(self.config)
                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\10010\src\unicom_monitor\data_storage.py", line 36, in __init__
    self.history_file_path = os.path.join(config.data_dir, config.history_file)
                                          ^^^^^^^^^^^^^^^
AttributeError: 'Config' object has no attribute 'data_dir'
2025-07-04 12:03:56,722 - root - INFO - 程序启动，开始解析参数...
2025-07-04 12:03:56,723 - root - INFO - 已启用调试模式 (DEBUG)
2025-07-04 12:03:56,723 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:03:56,724 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 12:03:56,724 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 12:03:56,727 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 12:03:56,727 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 12:03:56,727 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 12:03:56,728 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 12:03:56,728 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 12:03:56,728 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 12:04:45,570 - root - INFO - 程序启动，开始解析参数...
2025-07-04 12:04:45,571 - root - INFO - 已启用调试模式 (DEBUG)
2025-07-04 12:04:45,571 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:04:45,572 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 12:04:45,572 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 12:04:45,574 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 12:04:45,575 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 12:04:45,575 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 12:04:45,575 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 12:04:45,575 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 12:04:45,575 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 12:04:45,575 - unicom_monitor.data_storage - INFO - 正在连接到MySQL数据库 192.168.0.115:3306
2025-07-04 12:05:30,650 - root - INFO - 程序启动，开始解析参数...
2025-07-04 12:05:30,653 - root - INFO - 已启用调试模式 (DEBUG)
2025-07-04 12:05:30,653 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:05:30,653 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 12:05:30,653 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 12:05:30,657 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 12:05:30,658 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 12:05:30,658 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 12:05:30,658 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 12:05:30,658 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 12:05:30,659 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 12:05:30,659 - unicom_monitor.data_storage - INFO - 正在连接到MySQL数据库 192.168.0.115:3306
2025-07-04 12:06:21,446 - root - INFO - 程序启动，开始解析参数...
2025-07-04 12:06:21,447 - root - INFO - 已启用调试模式 (DEBUG)
2025-07-04 12:06:21,447 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:06:21,447 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 12:06:21,448 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 12:06:21,450 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 12:06:21,451 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 12:06:21,451 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 12:06:21,451 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 12:06:21,451 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 12:06:21,451 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 12:06:21,451 - unicom_monitor.data_storage - WARNING - 暂时跳过MySQL连接，使用文件存储
2025-07-04 12:06:21,452 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-04 12:06:21,452 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-04 12:06:21,452 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-04 12:06:21,452 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-04 12:06:21,452 - unicom_monitor.monitor - INFO - 启动联通手机号监控系统
2025-07-04 12:06:21,452 - unicom_monitor.monitor - INFO - 配置信息:
2025-07-04 12:06:21,452 - unicom_monitor.monitor - INFO -   - 请求间隔: 20 秒
2025-07-04 12:06:21,452 - unicom_monitor.monitor - INFO -   - 最大重试次数: 3
2025-07-04 12:06:21,452 - unicom_monitor.monitor - INFO -   - 数据存储: MySQL (192.168.0.115:3306)
2025-07-04 12:06:21,453 - unicom_monitor.monitor - INFO -   - 钉钉通知: 已配置
2025-07-04 12:06:21,453 - unicom_monitor.monitor - INFO - 测试钉钉连接...
2025-07-04 12:06:21,457 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): oapi.dingtalk.com:443
2025-07-04 12:06:21,568 - urllib3.connectionpool - DEBUG - https://oapi.dingtalk.com:443 "POST /robot/send?access_token=2ba5229956f0aed9b947a6a1211ecd9699189f253de1cc4fbba9479d1388096b&timestamp=1751601981453&sign=Xbnbt54JoPcibSGAbETlzVv%2Bzo%2FjNy8Lnp%2FjdPrFtFM%3D HTTP/1.1" 200 None
2025-07-04 12:06:21,569 - unicom_monitor.dingtalk_notifier - INFO - 钉钉消息发送成功
2025-07-04 12:06:21,569 - unicom_monitor.monitor - INFO - 钉钉连接测试成功
2025-07-04 12:06:21,569 - unicom_monitor.monitor - INFO - 历史统计: 已处理 0 个号码，发现 0 个特殊号码
2025-07-04 12:06:21,569 - unicom_monitor.monitor - INFO - 开始监控循环
2025-07-04 12:06:21,569 - unicom_monitor.monitor - INFO - 监控系统已启动
2025-07-04 12:06:21,570 - unicom_monitor.api_client - INFO - 正在初始化获取UNICOM_TOKEN...
2025-07-04 12:06:21,570 - unicom_monitor.token_manager - INFO - 开始刷新联通TOKEN...
2025-07-04 12:06:21,570 - unicom_monitor.token_manager - INFO - 正在获取ticket...
2025-07-04 12:06:21,571 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): m.client.10010.com:443
2025-07-04 12:06:21,673 - urllib3.connectionpool - DEBUG - https://m.client.10010.com:443 "GET /mobileService/openPlatform/openPlatLineNew.htm?to_url=https%3A%2F%2Fcard.10010.com%2Fterminal%2FsecondaryCardCom%3Fpull_login%3D1%26salesId%3D98X2103051447472194%26channel%3D06-0324-aseo-b29o HTTP/1.1" 302 None
2025-07-04 12:06:21,674 - unicom_monitor.token_manager - INFO - 成功获取ticket: alu8zuwg49...
2025-07-04 12:06:21,674 - unicom_monitor.token_manager - INFO - 正在使用ticket获取numToken...
2025-07-04 12:06:21,675 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): card.10010.com:443
2025-07-04 12:06:21,879 - urllib3.connectionpool - DEBUG - https://card.10010.com:443 "GET /mall-order/ticket/check/v1?ticket=alu8zuwg49e85c6f9ef7b9a6a12d5833b17962bfoklrm4zd&reqType=3 HTTP/1.1" 200 None
2025-07-04 12:06:21,881 - unicom_monitor.token_manager - INFO - 成功获取并刷新numToken
2025-07-04 12:06:21,881 - unicom_monitor.monitor - INFO - 正在获取手机号码...
2025-07-04 12:06:21,881 - unicom_monitor.api_client - INFO - 发送API请求 (尝试 1/3)
2025-07-04 12:06:21,883 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): card.10010.com:443
2025-07-04 12:06:22,366 - urllib3.connectionpool - DEBUG - https://card.10010.com:443 "GET /NumDockerApp/NumberCenter/qryNum/newRecToken?provinceCode=17&cityCode=166&numClass=1&amounts=100&channel=B2C&chnlType=1&groupType=1&callType=1&monitorPurpose=1009&searchRatio=100&searchValue=&searchCode=02&jsonp=callback&searchType=1&serialNumber=&selectRule=306%2C307%2C308%2C310%2C311%2C315%2C316%2C317%2C318%2C322&goodsId=&callback=jsonp_1751601981881_82380&token=x7fHub9URZl8qRlMVBKkrgi%2BqS9e6j1G3Sk0ujrb%2BGQ2LqFSmpiNuh1Bzop53VnXlYeaiU42QZDPWg5hn7NvIuM8GU6vuL%2FN6TWvy0qWRMPEaUGJcmvEsA5KNBaXWqVjNaxxQs2nJULu%2BwtydrEHA%2Bbtk5WWpg4dw6Q1Rwq%2Bla8KRi5ep5ma4KGlXoTchc9r3HbXq3gGbX4IEfCaFr3wHjvLQGtvWkUXbpT%2FQ5ycI6A%3D HTTP/1.1" 200 10457
2025-07-04 12:06:22,367 - unicom_monitor.api_client - INFO - 从API响应中提取到 100 个手机号码
2025-07-04 12:06:22,367 - unicom_monitor.api_client - INFO - 成功获取 100 个手机号码
2025-07-04 12:06:22,368 - unicom_monitor.monitor - INFO - 获取到 100 个手机号码
2025-07-04 12:06:22,368 - unicom_monitor.monitor - INFO - 发现 100 个新号码
2025-07-04 12:06:22,368 - unicom_monitor.number_analyzer - INFO - 分析 100 个新号码
2025-07-04 12:06:22,369 - unicom_monitor.number_analyzer - INFO - 发现 1 个特殊号码
2025-07-04 12:06:22,369 - unicom_monitor.number_analyzer - INFO -   尾号三回旋: 945945: 17660945945
2025-07-04 12:06:22,369 - unicom_monitor.monitor - INFO - 没有新的特殊号码
2025-07-04 12:06:22,369 - unicom_monitor.monitor - INFO - 等待 20 秒后进行下次检查...
2025-07-04 12:06:42,031 - unicom_monitor.monitor - INFO - 接收到信号 2，准备停止监控...
2025-07-04 12:06:42,032 - unicom_monitor.monitor - INFO - 正在停止监控系统...
2025-07-04 12:06:42,369 - unicom_monitor.monitor - INFO - 监控循环结束
2025-07-04 12:06:42,370 - unicom_monitor.monitor - INFO - 监控系统已停止
2025-07-04 12:06:42,576 - unicom_monitor.monitor - WARNING - 监控未在运行
2025-07-04 12:06:56,271 - root - INFO - 程序启动，开始解析参数...
2025-07-04 12:06:56,272 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:06:56,272 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 12:06:56,272 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 12:06:56,275 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 12:06:56,275 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 12:06:56,276 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 12:06:56,276 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 12:06:56,276 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 12:06:56,276 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 12:06:56,276 - unicom_monitor.data_storage - WARNING - 暂时跳过MySQL连接，使用文件存储
2025-07-04 12:06:56,277 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-04 12:06:56,277 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-04 12:06:56,277 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-04 12:06:56,277 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-04 12:07:06,420 - root - INFO - 程序启动，开始解析参数...
2025-07-04 12:07:06,421 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:07:06,421 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 12:07:06,421 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 12:07:06,424 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 12:07:06,424 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 12:07:06,425 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 12:07:06,425 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 12:07:06,425 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 12:07:06,425 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 12:07:06,425 - unicom_monitor.data_storage - WARNING - 暂时跳过MySQL连接，使用文件存储
2025-07-04 12:07:06,426 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-04 12:07:06,426 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-04 12:07:06,426 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-04 12:07:06,426 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-04 12:07:06,426 - unicom_monitor.monitor - INFO - 启动联通手机号监控系统
2025-07-04 12:07:06,426 - unicom_monitor.monitor - INFO - 配置信息:
2025-07-04 12:07:06,426 - unicom_monitor.monitor - INFO -   - 请求间隔: 20 秒
2025-07-04 12:07:06,427 - unicom_monitor.monitor - INFO -   - 最大重试次数: 3
2025-07-04 12:07:06,427 - unicom_monitor.monitor - INFO -   - 数据存储: MySQL (192.168.0.115:3306)
2025-07-04 12:07:06,427 - unicom_monitor.monitor - INFO -   - 钉钉通知: 已配置
2025-07-04 12:07:06,427 - unicom_monitor.monitor - INFO - 测试钉钉连接...
2025-07-04 12:07:06,570 - unicom_monitor.dingtalk_notifier - INFO - 钉钉消息发送成功
2025-07-04 12:07:06,570 - unicom_monitor.monitor - INFO - 钉钉连接测试成功
2025-07-04 12:07:06,570 - unicom_monitor.monitor - INFO - 历史统计: 已处理 0 个号码，发现 0 个特殊号码
2025-07-04 12:07:06,570 - unicom_monitor.monitor - INFO - 开始监控循环
2025-07-04 12:07:06,570 - unicom_monitor.monitor - INFO - 监控系统已启动
2025-07-04 12:07:06,570 - unicom_monitor.api_client - INFO - 正在初始化获取UNICOM_TOKEN...
2025-07-04 12:07:06,571 - unicom_monitor.token_manager - INFO - 开始刷新联通TOKEN...
2025-07-04 12:07:06,571 - unicom_monitor.token_manager - INFO - 正在获取ticket...
2025-07-04 12:07:06,922 - unicom_monitor.token_manager - INFO - 成功获取ticket: ojcbvs5ic3...
2025-07-04 12:07:06,922 - unicom_monitor.token_manager - INFO - 正在使用ticket获取numToken...
2025-07-04 12:07:07,152 - unicom_monitor.token_manager - INFO - 成功获取并刷新numToken
2025-07-04 12:07:07,152 - unicom_monitor.monitor - INFO - 正在获取手机号码...
2025-07-04 12:07:07,152 - unicom_monitor.api_client - INFO - 发送API请求 (尝试 1/3)
2025-07-04 12:07:07,570 - unicom_monitor.api_client - INFO - 从API响应中提取到 100 个手机号码
2025-07-04 12:07:07,570 - unicom_monitor.api_client - INFO - 成功获取 100 个手机号码
2025-07-04 12:07:07,570 - unicom_monitor.monitor - INFO - 获取到 100 个手机号码
2025-07-04 12:07:07,571 - unicom_monitor.monitor - INFO - 发现 100 个新号码
2025-07-04 12:07:07,571 - unicom_monitor.number_analyzer - INFO - 分析 100 个新号码
2025-07-04 12:07:07,572 - unicom_monitor.monitor - INFO - 没有新的特殊号码
2025-07-04 12:07:07,572 - unicom_monitor.monitor - INFO - 等待 20 秒后进行下次检查...
2025-07-04 12:07:27,572 - unicom_monitor.monitor - INFO - 正在获取手机号码...
2025-07-04 12:07:27,572 - unicom_monitor.api_client - INFO - 发送API请求 (尝试 1/3)
2025-07-04 12:07:27,932 - unicom_monitor.api_client - INFO - 从API响应中提取到 100 个手机号码
2025-07-04 12:07:27,932 - unicom_monitor.api_client - INFO - 成功获取 100 个手机号码
2025-07-04 12:07:27,932 - unicom_monitor.monitor - INFO - 获取到 100 个手机号码
2025-07-04 12:07:27,932 - unicom_monitor.monitor - INFO - 发现 100 个新号码
2025-07-04 12:07:27,932 - unicom_monitor.number_analyzer - INFO - 分析 60 个新号码
2025-07-04 12:07:27,933 - unicom_monitor.monitor - INFO - 没有新的特殊号码
2025-07-04 12:07:27,933 - unicom_monitor.monitor - INFO - 等待 20 秒后进行下次检查...
2025-07-04 12:09:12,304 - root - INFO - 程序启动，开始解析参数...
2025-07-04 12:09:12,305 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:09:12,305 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 12:09:12,306 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 12:09:12,308 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 12:09:12,309 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 12:09:12,309 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 12:09:12,309 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 12:09:12,309 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 12:09:12,309 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 12:09:12,309 - unicom_monitor.data_storage - WARNING - 暂时跳过MySQL连接，使用文件存储
2025-07-04 12:09:12,310 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-04 12:09:12,310 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-04 12:09:12,310 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-04 12:09:12,310 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-04 12:14:50,161 - root - INFO - 程序启动，开始解析参数...
2025-07-04 12:14:50,162 - root - INFO - 已启用调试模式 (DEBUG)
2025-07-04 12:14:50,163 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:14:50,163 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 12:14:50,163 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 12:14:50,166 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 12:14:50,166 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 12:14:50,167 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 12:14:50,167 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 12:14:50,167 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 12:14:50,167 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 12:14:50,167 - unicom_monitor.data_storage - INFO - 正在连接到MySQL数据库 192.168.0.115:3306
2025-07-04 12:14:50,169 - unicom_monitor.data_storage - INFO - 成功连接到MySQL数据库
2025-07-04 12:14:50,421 - unicom_monitor.data_storage - INFO - 数据表创建/检查完成
2025-07-04 12:14:50,421 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-04 12:14:50,421 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-04 12:14:50,421 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-04 12:14:50,422 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-04 12:14:50,422 - unicom_monitor.monitor - INFO - 启动联通手机号监控系统
2025-07-04 12:14:50,422 - unicom_monitor.monitor - INFO - 配置信息:
2025-07-04 12:14:50,423 - unicom_monitor.monitor - INFO -   - 请求间隔: 20 秒
2025-07-04 12:14:50,423 - unicom_monitor.monitor - INFO -   - 最大重试次数: 3
2025-07-04 12:14:50,423 - unicom_monitor.monitor - INFO -   - 数据存储: MySQL (192.168.0.115:3306)
2025-07-04 12:14:50,423 - unicom_monitor.monitor - INFO -   - 钉钉通知: 已配置
2025-07-04 12:14:50,423 - unicom_monitor.monitor - INFO - 测试钉钉连接...
2025-07-04 12:14:50,426 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): oapi.dingtalk.com:443
2025-07-04 12:14:50,544 - urllib3.connectionpool - DEBUG - https://oapi.dingtalk.com:443 "POST /robot/send?access_token=2ba5229956f0aed9b947a6a1211ecd9699189f253de1cc4fbba9479d1388096b&timestamp=1751602490423&sign=g3VtULMfxDo6Cro4X%2BwfK%2BtywtRN%2FV66uZ%2FhRlsFqns%3D HTTP/1.1" 200 None
2025-07-04 12:14:50,544 - unicom_monitor.dingtalk_notifier - INFO - 钉钉消息发送成功
2025-07-04 12:14:50,544 - unicom_monitor.monitor - INFO - 钉钉连接测试成功
2025-07-04 12:14:50,548 - unicom_monitor.monitor - INFO - 历史统计: 已处理 0 个号码，发现 0 个特殊号码
2025-07-04 12:14:50,549 - unicom_monitor.monitor - INFO - 开始监控循环
2025-07-04 12:14:50,549 - unicom_monitor.monitor - INFO - 监控系统已启动
2025-07-04 12:14:50,549 - unicom_monitor.api_client - INFO - 正在初始化获取UNICOM_TOKEN...
2025-07-04 12:14:50,549 - unicom_monitor.token_manager - INFO - 开始刷新联通TOKEN...
2025-07-04 12:14:50,549 - unicom_monitor.token_manager - INFO - 正在获取ticket...
2025-07-04 12:14:50,550 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): m.client.10010.com:443
2025-07-04 12:15:00,601 - unicom_monitor.token_manager - ERROR - 获取ticket请求失败: HTTPSConnectionPool(host='m.client.10010.com', port=443): Read timed out. (read timeout=10)
2025-07-04 12:15:00,601 - unicom_monitor.api_client - ERROR - 初始化Token失败: 获取ticket请求失败: HTTPSConnectionPool(host='m.client.10010.com', port=443): Read timed out. (read timeout=10)
2025-07-04 12:15:00,602 - unicom_monitor.monitor - ERROR - 无法启动监控，因为首次Token获取失败
2025-07-04 12:15:01,553 - unicom_monitor.monitor - WARNING - 监控未在运行
2025-07-04 12:15:48,774 - root - INFO - 程序启动，开始解析参数...
2025-07-04 12:15:48,776 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:15:48,776 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 12:15:48,776 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 12:15:48,779 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 12:15:48,779 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 12:15:48,779 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 12:15:48,779 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 12:15:48,779 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 12:15:48,780 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 12:15:48,780 - unicom_monitor.data_storage - INFO - 正在连接到MySQL数据库 192.168.0.115:3306
2025-07-04 12:15:48,782 - unicom_monitor.data_storage - INFO - 成功连接到MySQL数据库
2025-07-04 12:15:48,783 - unicom_monitor.data_storage - INFO - 数据表创建/检查完成
2025-07-04 12:15:48,783 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-04 12:15:48,784 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-04 12:15:48,784 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-04 12:15:48,785 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-04 12:15:48,785 - unicom_monitor.monitor - INFO - 执行单次检查...
2025-07-04 12:15:48,785 - unicom_monitor.api_client - INFO - 正在初始化获取UNICOM_TOKEN...
2025-07-04 12:15:48,785 - unicom_monitor.token_manager - INFO - 开始刷新联通TOKEN...
2025-07-04 12:15:48,785 - unicom_monitor.token_manager - INFO - 正在获取ticket...
2025-07-04 12:15:49,586 - unicom_monitor.token_manager - INFO - 成功获取ticket: gjfbrhis62...
2025-07-04 12:15:49,587 - unicom_monitor.token_manager - INFO - 正在使用ticket获取numToken...
2025-07-04 12:15:59,655 - unicom_monitor.token_manager - ERROR - 获取numToken请求失败: HTTPSConnectionPool(host='card.10010.com', port=443): Read timed out. (read timeout=10)
2025-07-04 12:15:59,655 - unicom_monitor.api_client - ERROR - 初始化Token失败: 获取numToken请求失败: HTTPSConnectionPool(host='card.10010.com', port=443): Read timed out. (read timeout=10)
2025-07-04 12:15:59,655 - unicom_monitor.api_client - ERROR - 无法启动监控，因为首次Token获取失败
2025-07-04 12:15:59,656 - unicom_monitor.monitor - WARNING - 未获取到任何手机号码
2025-07-04 12:51:06,318 - root - INFO - 程序启动，开始解析参数...
2025-07-04 12:51:06,319 - root - INFO - 已启用调试模式 (DEBUG)
2025-07-04 12:51:06,319 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:51:06,320 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 12:51:06,320 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 12:51:06,323 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 12:51:06,323 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 12:51:06,323 - unicom_monitor.db_manager - INFO - 初始化数据库连接池，大小: 5
2025-07-04 12:51:06,325 - unicom_monitor.db_manager - DEBUG - 创建新的数据库连接
2025-07-04 12:51:06,326 - unicom_monitor.db_manager - DEBUG - 创建新的数据库连接
2025-07-04 12:51:06,330 - unicom_monitor.db_manager - DEBUG - 创建新的数据库连接
2025-07-04 12:51:06,331 - unicom_monitor.db_manager - DEBUG - 创建新的数据库连接
2025-07-04 12:51:06,332 - unicom_monitor.db_manager - DEBUG - 创建新的数据库连接
2025-07-04 12:51:06,332 - unicom_monitor.db_manager - INFO - 连接池初始化完成，当前连接数: 5
2025-07-04 12:51:06,332 - unicom_monitor.db_manager - DEBUG - 执行连接池健康检查
2025-07-04 12:51:06,334 - unicom_monitor.db_manager - INFO - 数据库连接池初始化成功
2025-07-04 12:51:06,335 - unicom_monitor.data_storage - INFO - 数据表创建/检查完成
2025-07-04 12:51:06,335 - unicom_monitor.data_storage - INFO - ✅ 成功连接到MySQL数据库
2025-07-04 12:51:06,335 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-04 12:51:06,335 - unicom_monitor.monitor - INFO - 获取数据库连接用于Token缓存
2025-07-04 12:51:06,336 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 12:51:06,445 - unicom_monitor.token_manager - INFO - Token缓存表创建/检查完成
2025-07-04 12:51:06,445 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 12:51:06,445 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 12:51:06,445 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 12:51:06,446 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-04 12:51:06,446 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-04 12:51:06,446 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-04 12:51:06,446 - unicom_monitor.monitor - INFO - 启动联通手机号监控系统
2025-07-04 12:51:06,446 - unicom_monitor.monitor - INFO - 配置信息:
2025-07-04 12:51:06,446 - unicom_monitor.monitor - INFO -   - 请求间隔: 20 秒
2025-07-04 12:51:06,446 - unicom_monitor.monitor - INFO -   - 最大重试次数: 3
2025-07-04 12:51:06,446 - unicom_monitor.monitor - INFO -   - 数据存储: MySQL (192.168.0.115:3306)
2025-07-04 12:51:06,447 - unicom_monitor.monitor - INFO -   - 钉钉通知: 已配置
2025-07-04 12:51:06,447 - unicom_monitor.monitor - INFO - 测试钉钉连接...
2025-07-04 12:51:06,447 - unicom_monitor.network_utils - DEBUG - 钉钉消息发送 - 尝试 1/5
2025-07-04 12:51:06,449 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): oapi.dingtalk.com:443
2025-07-04 12:51:06,573 - urllib3.connectionpool - DEBUG - https://oapi.dingtalk.com:443 "POST /robot/send?access_token=2ba5229956f0aed9b947a6a1211ecd9699189f253de1cc4fbba9479d1388096b&timestamp=1751604666447&sign=%2FRPWCcBCj5696dmj369qDJWdU1kdkopF0qCtu0Jfa0o%3D HTTP/1.1" 200 None
2025-07-04 12:51:06,574 - unicom_monitor.dingtalk_notifier - INFO - 钉钉消息发送成功
2025-07-04 12:51:06,574 - unicom_monitor.monitor - INFO - 钉钉连接测试成功
2025-07-04 12:51:06,576 - unicom_monitor.monitor - INFO - 历史统计: 已处理 0 个号码，发现 0 个特殊号码
2025-07-04 12:51:06,577 - unicom_monitor.monitor - INFO - 开始监控循环
2025-07-04 12:51:06,577 - unicom_monitor.monitor - INFO - 监控系统已启动
2025-07-04 12:51:06,577 - unicom_monitor.api_client - INFO - 正在初始化获取UNICOM_TOKEN...
2025-07-04 12:51:06,578 - unicom_monitor.token_manager - INFO - 开始获取新的联通API Token...
2025-07-04 12:51:06,578 - unicom_monitor.network_utils - DEBUG - 获取ticket - 尝试 1/5
2025-07-04 12:51:06,579 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): m.client.10010.com:443
2025-07-04 12:51:06,691 - urllib3.connectionpool - DEBUG - https://m.client.10010.com:443 "GET /mobileService/customer/accountListData.htm HTTP/1.1" 200 None
2025-07-04 12:51:06,691 - unicom_monitor.network_utils - WARNING - 获取ticket - 请求错误，2.0秒后重试: Expecting value: line 5 column 1 (char 4)
2025-07-04 12:51:08,692 - unicom_monitor.network_utils - DEBUG - 获取ticket - 尝试 2/5
2025-07-04 12:51:08,693 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): m.client.10010.com:443
2025-07-04 12:51:08,815 - urllib3.connectionpool - DEBUG - https://m.client.10010.com:443 "GET /mobileService/customer/accountListData.htm HTTP/1.1" 200 None
2025-07-04 12:51:08,816 - unicom_monitor.network_utils - WARNING - 获取ticket - 请求错误，4.0秒后重试: Expecting value: line 5 column 1 (char 4)
2025-07-04 12:51:12,816 - unicom_monitor.network_utils - DEBUG - 获取ticket - 尝试 3/5
2025-07-04 12:51:12,817 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): m.client.10010.com:443
2025-07-04 12:51:27,865 - unicom_monitor.network_utils - WARNING - 获取ticket - 超时错误 (第1次)，8.0秒后重试: HTTPSConnectionPool(host='m.client.10010.com', port=443): Read timed out. (read timeout=15)
2025-07-04 12:51:35,866 - unicom_monitor.network_utils - DEBUG - 获取ticket - 尝试 4/5
2025-07-04 12:51:35,867 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): m.client.10010.com:443
2025-07-04 12:51:35,978 - urllib3.connectionpool - DEBUG - https://m.client.10010.com:443 "GET /mobileService/customer/accountListData.htm HTTP/1.1" 200 None
2025-07-04 12:51:35,979 - unicom_monitor.network_utils - WARNING - 获取ticket - 请求错误，16.0秒后重试: Expecting value: line 5 column 1 (char 4)
2025-07-04 12:51:42,842 - unicom_monitor.monitor - INFO - 接收到信号 2，准备停止监控...
2025-07-04 12:51:42,843 - unicom_monitor.monitor - INFO - 正在停止监控系统...
2025-07-04 12:51:51,979 - unicom_monitor.network_utils - DEBUG - 获取ticket - 尝试 5/5
2025-07-04 12:51:51,980 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): m.client.10010.com:443
2025-07-04 12:51:52,153 - urllib3.connectionpool - DEBUG - https://m.client.10010.com:443 "GET /mobileService/customer/accountListData.htm HTTP/1.1" 200 None
2025-07-04 12:51:52,154 - unicom_monitor.network_utils - ERROR - 获取ticket - 请求错误，已达最大重试次数
2025-07-04 12:51:52,154 - unicom_monitor.network_utils - ERROR - 获取ticket - 所有重试都失败
2025-07-04 12:51:52,154 - unicom_monitor.token_manager - ERROR - 获取ticket失败: Expecting value: line 5 column 1 (char 4)
2025-07-04 12:51:52,154 - unicom_monitor.api_client - ERROR - 无法获取有效Token
2025-07-04 12:51:52,154 - unicom_monitor.monitor - ERROR - 无法启动监控，因为首次Token获取失败
2025-07-04 12:51:52,155 - unicom_monitor.monitor - INFO - 接收到信号 2，准备停止监控...
2025-07-04 12:51:52,155 - unicom_monitor.monitor - WARNING - 监控未在运行
2025-07-04 12:51:52,155 - unicom_monitor.db_manager - INFO - 关闭所有数据库连接
2025-07-04 12:51:52,155 - unicom_monitor.data_storage - INFO - 数据库连接已关闭
2025-07-04 12:51:52,155 - unicom_monitor.monitor - INFO - 监控系统已停止
2025-07-04 12:51:52,156 - unicom_monitor.monitor - WARNING - 监控未在运行
2025-07-04 12:51:54,508 - root - INFO - 程序启动，开始解析参数...
2025-07-04 12:51:54,509 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 12:51:54,509 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 12:51:54,509 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 12:51:54,512 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 12:51:54,512 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 12:51:54,513 - unicom_monitor.db_manager - INFO - 初始化数据库连接池，大小: 5
2025-07-04 12:51:54,522 - unicom_monitor.db_manager - INFO - 连接池初始化完成，当前连接数: 5
2025-07-04 12:51:54,525 - unicom_monitor.db_manager - INFO - 数据库连接池初始化成功
2025-07-04 12:51:54,527 - unicom_monitor.data_storage - INFO - 数据表创建/检查完成
2025-07-04 12:51:54,527 - unicom_monitor.data_storage - INFO - ✅ 成功连接到MySQL数据库
2025-07-04 12:51:54,527 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-04 12:51:54,528 - unicom_monitor.monitor - INFO - 获取数据库连接用于Token缓存
2025-07-04 12:51:54,528 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 12:51:54,529 - unicom_monitor.token_manager - INFO - Token缓存表创建/检查完成
2025-07-04 12:51:54,529 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 12:51:54,529 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 12:51:54,530 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 12:51:54,530 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-04 12:51:54,530 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-04 12:51:54,530 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-04 13:03:14,320 - root - INFO - 程序启动，开始解析参数...
2025-07-04 13:03:14,322 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 13:03:14,322 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 13:03:14,322 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 13:03:14,325 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 13:03:14,325 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 13:03:14,325 - unicom_monitor.db_manager - INFO - 初始化数据库连接池，大小: 5
2025-07-04 13:03:14,335 - unicom_monitor.db_manager - INFO - 连接池初始化完成，当前连接数: 5
2025-07-04 13:03:14,338 - unicom_monitor.db_manager - INFO - 数据库连接池初始化成功
2025-07-04 13:03:14,339 - unicom_monitor.data_storage - INFO - 数据表创建/检查完成
2025-07-04 13:03:14,339 - unicom_monitor.data_storage - INFO - ✅ 成功连接到MySQL数据库
2025-07-04 13:03:14,340 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-04 13:03:14,340 - unicom_monitor.monitor - INFO - 获取数据库连接用于Token缓存
2025-07-04 13:03:14,340 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 13:03:14,341 - unicom_monitor.token_manager - INFO - Token缓存表创建/检查完成
2025-07-04 13:03:14,341 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 13:03:14,341 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 13:03:14,341 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 13:03:14,341 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-04 13:03:14,341 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-04 13:03:14,342 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-04 13:03:14,342 - unicom_monitor.monitor - INFO - 启动联通手机号监控系统
2025-07-04 13:03:14,342 - unicom_monitor.monitor - INFO - 配置信息:
2025-07-04 13:03:14,342 - unicom_monitor.monitor - INFO -   - 请求间隔: 20 秒
2025-07-04 13:03:14,342 - unicom_monitor.monitor - INFO -   - 最大重试次数: 3
2025-07-04 13:03:14,342 - unicom_monitor.monitor - INFO -   - 数据存储: MySQL (192.168.0.115:3306)
2025-07-04 13:03:14,342 - unicom_monitor.monitor - INFO -   - 钉钉通知: 已配置
2025-07-04 13:03:14,342 - unicom_monitor.monitor - INFO - 测试钉钉连接...
2025-07-04 13:03:14,453 - unicom_monitor.dingtalk_notifier - INFO - 钉钉消息发送成功
2025-07-04 13:03:14,453 - unicom_monitor.monitor - INFO - 钉钉连接测试成功
2025-07-04 13:03:14,455 - unicom_monitor.monitor - INFO - 历史统计: 已处理 0 个号码，发现 0 个特殊号码
2025-07-04 13:03:14,456 - unicom_monitor.monitor - INFO - 开始监控循环
2025-07-04 13:03:14,456 - unicom_monitor.monitor - INFO - 监控系统已启动
2025-07-04 13:03:14,456 - unicom_monitor.api_client - INFO - 正在初始化获取UNICOM_TOKEN...
2025-07-04 13:03:14,457 - unicom_monitor.token_manager - INFO - 开始获取新的联通API Token...
2025-07-04 13:03:14,830 - unicom_monitor.network_utils - WARNING - 获取ticket - 请求错误，2.0秒后重试: Expecting value: line 5 column 1 (char 4)
2025-07-04 13:03:16,938 - unicom_monitor.network_utils - WARNING - 获取ticket - 请求错误，4.0秒后重试: Expecting value: line 5 column 1 (char 4)
2025-07-04 13:03:35,966 - unicom_monitor.network_utils - WARNING - 获取ticket - 超时错误 (第1次)，8.0秒后重试: HTTPSConnectionPool(host='m.client.10010.com', port=443): Read timed out. (read timeout=15)
2025-07-04 13:03:44,073 - unicom_monitor.network_utils - WARNING - 获取ticket - 请求错误，16.0秒后重试: Expecting value: line 5 column 1 (char 4)
2025-07-04 13:04:03,178 - unicom_monitor.network_utils - ERROR - 获取ticket - 请求错误，已达最大重试次数
2025-07-04 13:04:03,178 - unicom_monitor.network_utils - ERROR - 获取ticket - 所有重试都失败
2025-07-04 13:04:03,178 - unicom_monitor.token_manager - ERROR - 获取ticket失败: Expecting value: line 5 column 1 (char 4)
2025-07-04 13:04:03,178 - unicom_monitor.api_client - ERROR - 无法获取有效Token
2025-07-04 13:04:03,179 - unicom_monitor.monitor - ERROR - 无法启动监控，因为首次Token获取失败
2025-07-04 13:04:03,471 - unicom_monitor.monitor - WARNING - 监控未在运行
2025-07-04 13:06:16,274 - root - INFO - 程序启动，开始解析参数...
2025-07-04 13:06:16,276 - root - INFO - 已启用调试模式 (DEBUG)
2025-07-04 13:06:16,276 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 13:06:16,276 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 13:06:16,276 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 13:06:16,279 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 13:06:16,280 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 13:06:16,280 - unicom_monitor.db_manager - INFO - 初始化数据库连接池，大小: 5
2025-07-04 13:06:16,282 - unicom_monitor.db_manager - DEBUG - 创建新的数据库连接
2025-07-04 13:06:16,283 - unicom_monitor.db_manager - DEBUG - 创建新的数据库连接
2025-07-04 13:06:16,284 - unicom_monitor.db_manager - DEBUG - 创建新的数据库连接
2025-07-04 13:06:16,285 - unicom_monitor.db_manager - DEBUG - 创建新的数据库连接
2025-07-04 13:06:16,286 - unicom_monitor.db_manager - DEBUG - 创建新的数据库连接
2025-07-04 13:06:16,286 - unicom_monitor.db_manager - INFO - 连接池初始化完成，当前连接数: 5
2025-07-04 13:06:16,286 - unicom_monitor.db_manager - DEBUG - 执行连接池健康检查
2025-07-04 13:06:16,288 - unicom_monitor.db_manager - INFO - 数据库连接池初始化成功
2025-07-04 13:06:16,288 - unicom_monitor.data_storage - INFO - 数据表创建/检查完成
2025-07-04 13:06:16,289 - unicom_monitor.data_storage - INFO - ✅ 成功连接到MySQL数据库
2025-07-04 13:06:16,289 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-04 13:06:16,289 - unicom_monitor.monitor - INFO - 获取数据库连接用于Token缓存
2025-07-04 13:06:16,289 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 13:06:16,290 - unicom_monitor.token_manager - INFO - Token缓存表创建/检查完成
2025-07-04 13:06:16,290 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 13:06:16,290 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 13:06:16,290 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 13:06:16,290 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-04 13:06:16,290 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-04 13:06:16,290 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-04 13:06:16,291 - unicom_monitor.monitor - INFO - 启动联通手机号监控系统
2025-07-04 13:06:16,291 - unicom_monitor.monitor - INFO - 配置信息:
2025-07-04 13:06:16,291 - unicom_monitor.monitor - INFO -   - 请求间隔: 20 秒
2025-07-04 13:06:16,291 - unicom_monitor.monitor - INFO -   - 最大重试次数: 3
2025-07-04 13:06:16,291 - unicom_monitor.monitor - INFO -   - 数据存储: MySQL (192.168.0.115:3306)
2025-07-04 13:06:16,291 - unicom_monitor.monitor - INFO -   - 钉钉通知: 已配置
2025-07-04 13:06:16,291 - unicom_monitor.monitor - INFO - 测试钉钉连接...
2025-07-04 13:06:16,291 - unicom_monitor.network_utils - DEBUG - 钉钉消息发送 - 尝试 1/5
2025-07-04 13:06:16,294 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): oapi.dingtalk.com:443
2025-07-04 13:06:16,426 - urllib3.connectionpool - DEBUG - https://oapi.dingtalk.com:443 "POST /robot/send?access_token=2ba5229956f0aed9b947a6a1211ecd9699189f253de1cc4fbba9479d1388096b&timestamp=1751605576292&sign=MtXNc0G6thTBJUjtQadXvhEeEpykM7AJJ3IdctHcLDQ%3D HTTP/1.1" 200 None
2025-07-04 13:06:16,427 - unicom_monitor.dingtalk_notifier - INFO - 钉钉消息发送成功
2025-07-04 13:06:16,427 - unicom_monitor.monitor - INFO - 钉钉连接测试成功
2025-07-04 13:06:16,428 - unicom_monitor.monitor - INFO - 历史统计: 已处理 0 个号码，发现 0 个特殊号码
2025-07-04 13:06:16,429 - unicom_monitor.monitor - INFO - 开始监控循环
2025-07-04 13:06:16,429 - unicom_monitor.monitor - INFO - 监控系统已启动
2025-07-04 13:06:16,429 - unicom_monitor.api_client - INFO - 正在初始化获取UNICOM_TOKEN...
2025-07-04 13:06:16,429 - unicom_monitor.token_manager - INFO - 开始获取新的联通API Token...
2025-07-04 13:06:16,429 - unicom_monitor.network_utils - DEBUG - 获取ticket - 尝试 1/5
2025-07-04 13:06:16,431 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): m.client.10010.com:443
2025-07-04 13:06:16,641 - urllib3.connectionpool - DEBUG - https://m.client.10010.com:443 "GET /mobileService/openPlatform/openPlatLineNew.htm?to_url=https%3A%2F%2Fcard.10010.com%2Fterminal%2FsecondaryCardCom%3Fpull_login%3D1%26salesId%3D98X2103051447472194%26channel%3D06-0324-aseo-b29o HTTP/1.1" 302 None
2025-07-04 13:06:16,643 - unicom_monitor.token_manager - INFO - 成功获取ticket: 8kpunz6353...
2025-07-04 13:06:16,643 - unicom_monitor.network_utils - DEBUG - 获取numToken - 尝试 1/5
2025-07-04 13:06:16,644 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): card.10010.com:443
2025-07-04 13:06:16,856 - urllib3.connectionpool - DEBUG - https://card.10010.com:443 "GET /mall-order/ticket/check/v1?ticket=8kpunz635365802df4937fa9bd71752568a3461bibgmzw7l&reqType=3 HTTP/1.1" 200 None
2025-07-04 13:06:16,858 - unicom_monitor.token_manager - INFO - 成功获取numToken
2025-07-04 13:06:16,862 - unicom_monitor.token_manager - INFO - Token已保存到缓存，过期时间: 2025-07-04 15:06:16.858178
2025-07-04 13:06:16,862 - unicom_monitor.monitor - INFO - 正在获取手机号码...
2025-07-04 13:06:16,863 - unicom_monitor.network_utils - DEBUG - API请求 - 尝试 1/5
2025-07-04 13:06:16,864 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): card.10010.com:443
2025-07-04 13:06:17,260 - urllib3.connectionpool - DEBUG - https://card.10010.com:443 "GET /NumDockerApp/NumberCenter/qryNum/newRecToken?provinceCode=17&cityCode=166&numClass=1&amounts=100&channel=B2C&chnlType=1&groupType=1&callType=1&monitorPurpose=1009&searchRatio=100&searchValue=&searchCode=02&jsonp=callback&searchType=1&serialNumber=&selectRule=306%2C307%2C308%2C310%2C311%2C315%2C316%2C317%2C318%2C322&goodsId=&callback=jsonp_1751605576863_15770&token=x7fHub9URZl8qRlMVBKkrpeO3xj74XeEvTo0Jrx8aVo2LqFSmpiNuh1Bzop53VnXlYeaiU42QZDPWg5hn7NvIuM8GU6vuL%2FN6TWvy0qWRMPEaUGJcmvEsA5KNBaXWqVjNaxxQs2nJULu%2BwtydrEHA%2Bbtk5WWpg4dw6Q1Rwq%2Bla8KRi5ep5ma4KGlXoTchc9rgE3KDX6FWDFE8RNKYpQSGJrdYjNMUTqg9KoMZE4Aj84%3D HTTP/1.1" 200 10448
2025-07-04 13:06:17,261 - unicom_monitor.api_client - INFO - 从API响应中提取到 100 个手机号码
2025-07-04 13:06:17,261 - unicom_monitor.api_client - INFO - 成功获取 100 个手机号码
2025-07-04 13:06:17,261 - unicom_monitor.monitor - INFO - 获取到 100 个手机号码
2025-07-04 13:06:17,316 - unicom_monitor.monitor - INFO - 发现 100 个新号码
2025-07-04 13:06:17,316 - unicom_monitor.number_analyzer - INFO - 分析 100 个新号码
2025-07-04 13:06:17,362 - unicom_monitor.data_storage - INFO - 成功标记 100 个号码为已处理
2025-07-04 13:06:17,363 - unicom_monitor.monitor - INFO - 没有新的特殊号码
2025-07-04 13:06:17,363 - unicom_monitor.monitor - INFO - 等待 20 秒后进行下次检查...
2025-07-04 13:06:37,363 - unicom_monitor.monitor - INFO - 正在获取手机号码...
2025-07-04 13:06:37,363 - unicom_monitor.network_utils - DEBUG - API请求 - 尝试 1/5
2025-07-04 13:06:37,699 - urllib3.connectionpool - DEBUG - https://card.10010.com:443 "GET /NumDockerApp/NumberCenter/qryNum/newRecToken?provinceCode=17&cityCode=166&numClass=1&amounts=100&channel=B2C&chnlType=1&groupType=1&callType=1&monitorPurpose=1009&searchRatio=100&searchValue=&searchCode=02&jsonp=callback&searchType=1&serialNumber=&selectRule=306%2C307%2C308%2C310%2C311%2C315%2C316%2C317%2C318%2C322&goodsId=&callback=jsonp_1751605597363_83561&token=x7fHub9URZl8qRlMVBKkrpeO3xj74XeEvTo0Jrx8aVo2LqFSmpiNuh1Bzop53VnXlYeaiU42QZDPWg5hn7NvIuM8GU6vuL%2FN6TWvy0qWRMPEaUGJcmvEsA5KNBaXWqVjNaxxQs2nJULu%2BwtydrEHA%2Bbtk5WWpg4dw6Q1Rwq%2Bla8KRi5ep5ma4KGlXoTchc9rgE3KDX6FWDFE8RNKYpQSGJrdYjNMUTqg9KoMZE4Aj84%3D HTTP/1.1" 200 10518
2025-07-04 13:06:37,699 - unicom_monitor.api_client - INFO - 从API响应中提取到 100 个手机号码
2025-07-04 13:06:37,699 - unicom_monitor.api_client - INFO - 成功获取 100 个手机号码
2025-07-04 13:06:37,700 - unicom_monitor.monitor - INFO - 获取到 100 个手机号码
2025-07-04 13:06:37,753 - unicom_monitor.monitor - INFO - 发现 60 个新号码
2025-07-04 13:06:37,753 - unicom_monitor.number_analyzer - INFO - 分析 60 个新号码
2025-07-04 13:06:37,762 - unicom_monitor.data_storage - INFO - 成功标记 60 个号码为已处理
2025-07-04 13:06:37,762 - unicom_monitor.monitor - INFO - 没有新的特殊号码
2025-07-04 13:06:37,762 - unicom_monitor.monitor - INFO - 等待 20 秒后进行下次检查...
2025-07-04 13:06:53,548 - unicom_monitor.monitor - INFO - 接收到信号 2，准备停止监控...
2025-07-04 13:06:53,548 - unicom_monitor.monitor - INFO - 正在停止监控系统...
2025-07-04 13:06:57,763 - unicom_monitor.monitor - INFO - 监控循环结束
2025-07-04 13:06:57,764 - unicom_monitor.db_manager - INFO - 关闭所有数据库连接
2025-07-04 13:06:57,764 - unicom_monitor.data_storage - INFO - 数据库连接已关闭
2025-07-04 13:06:57,764 - unicom_monitor.monitor - INFO - 监控系统已停止
2025-07-04 13:06:57,764 - unicom_monitor.monitor - WARNING - 监控未在运行
2025-07-04 13:07:01,898 - root - INFO - 程序启动，开始解析参数...
2025-07-04 13:07:01,900 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 13:07:01,900 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 13:07:01,900 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 13:07:01,903 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 13:07:01,903 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 13:07:01,903 - unicom_monitor.db_manager - INFO - 初始化数据库连接池，大小: 5
2025-07-04 13:07:01,908 - unicom_monitor.db_manager - INFO - 连接池初始化完成，当前连接数: 5
2025-07-04 13:07:01,910 - unicom_monitor.db_manager - INFO - 数据库连接池初始化成功
2025-07-04 13:07:01,911 - unicom_monitor.data_storage - INFO - 数据表创建/检查完成
2025-07-04 13:07:01,911 - unicom_monitor.data_storage - INFO - ✅ 成功连接到MySQL数据库
2025-07-04 13:07:01,911 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-04 13:07:01,912 - unicom_monitor.monitor - INFO - 获取数据库连接用于Token缓存
2025-07-04 13:07:01,912 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 13:07:01,912 - unicom_monitor.token_manager - INFO - Token缓存表创建/检查完成
2025-07-04 13:07:01,912 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 13:07:01,912 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 13:07:01,913 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 13:07:01,913 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-04 13:07:01,913 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-04 13:07:01,913 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-04 13:07:01,913 - unicom_monitor.monitor - INFO - 执行单次检查...
2025-07-04 13:07:01,913 - unicom_monitor.api_client - INFO - 正在初始化获取UNICOM_TOKEN...
2025-07-04 13:07:01,914 - unicom_monitor.token_manager - INFO - 成功加载缓存Token，过期时间: 2025-07-04 15:06:16
2025-07-04 13:07:02,325 - unicom_monitor.api_client - INFO - 从API响应中提取到 100 个手机号码
2025-07-04 13:07:02,325 - unicom_monitor.api_client - INFO - 成功获取 100 个手机号码
2025-07-04 13:07:02,326 - unicom_monitor.monitor - INFO - 获取到 100 个手机号码
2025-07-04 13:07:02,326 - unicom_monitor.number_analyzer - INFO - 分析 100 个新号码
2025-07-04 13:07:02,327 - unicom_monitor.monitor - INFO - 未发现特殊号码
2025-07-04 13:07:02,370 - unicom_monitor.data_storage - INFO - 成功标记 100 个号码为已处理
2025-07-04 13:07:12,694 - root - INFO - 程序启动，开始解析参数...
2025-07-04 13:07:12,696 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-04 13:07:12,696 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-04 13:07:12,696 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-04 13:07:12,699 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-04 13:07:12,699 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-04 13:07:12,699 - unicom_monitor.db_manager - INFO - 初始化数据库连接池，大小: 5
2025-07-04 13:07:12,709 - unicom_monitor.db_manager - INFO - 连接池初始化完成，当前连接数: 5
2025-07-04 13:07:12,711 - unicom_monitor.db_manager - INFO - 数据库连接池初始化成功
2025-07-04 13:07:12,713 - unicom_monitor.data_storage - INFO - 数据表创建/检查完成
2025-07-04 13:07:12,713 - unicom_monitor.data_storage - INFO - ✅ 成功连接到MySQL数据库
2025-07-04 13:07:12,713 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-04 13:07:12,714 - unicom_monitor.monitor - INFO - 获取数据库连接用于Token缓存
2025-07-04 13:07:12,714 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-04 13:07:12,715 - unicom_monitor.token_manager - INFO - Token缓存表创建/检查完成
2025-07-04 13:07:12,715 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-04 13:07:12,715 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-04 13:07:12,715 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-04 13:07:12,715 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-04 13:07:12,716 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-04 13:07:12,716 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-06 19:26:49,750 - root - INFO - 程序启动，开始解析参数...
2025-07-06 19:26:49,752 - root - INFO - 正在创建PhoneNumberMonitor实例...
2025-07-06 19:26:49,752 - unicom_monitor.monitor - INFO - 开始初始化监控器，配置文件: config.env
2025-07-06 19:26:49,752 - unicom_monitor.monitor - INFO - 正在加载配置...
2025-07-06 19:26:49,755 - unicom_monitor.monitor - INFO - 配置加载成功
2025-07-06 19:26:49,755 - unicom_monitor.monitor - INFO - 正在创建数据存储...
2025-07-06 19:26:49,756 - unicom_monitor.db_manager - INFO - 初始化数据库连接池，大小: 5
2025-07-06 19:26:49,764 - unicom_monitor.db_manager - INFO - 连接池初始化完成，当前连接数: 5
2025-07-06 19:26:49,767 - unicom_monitor.db_manager - INFO - 数据库连接池初始化成功
2025-07-06 19:26:49,769 - unicom_monitor.data_storage - INFO - 数据表创建/检查完成
2025-07-06 19:26:49,769 - unicom_monitor.data_storage - INFO - ✅ 成功连接到MySQL数据库
2025-07-06 19:26:49,769 - unicom_monitor.monitor - INFO - 数据存储创建成功
2025-07-06 19:26:49,770 - unicom_monitor.monitor - INFO - 获取数据库连接用于Token缓存
2025-07-06 19:26:49,770 - unicom_monitor.monitor - INFO - 正在创建API客户端...
2025-07-06 19:26:49,771 - unicom_monitor.token_manager - INFO - Token缓存表创建/检查完成
2025-07-06 19:26:49,771 - unicom_monitor.monitor - INFO - API客户端创建成功
2025-07-06 19:26:49,771 - unicom_monitor.monitor - INFO - 正在创建号码分析器...
2025-07-06 19:26:49,771 - unicom_monitor.monitor - INFO - 号码分析器创建成功
2025-07-06 19:26:49,771 - unicom_monitor.monitor - INFO - 正在创建钉钉通知器...
2025-07-06 19:26:49,771 - unicom_monitor.monitor - INFO - 钉钉通知器创建成功
2025-07-06 19:26:49,771 - root - INFO - PhoneNumberMonitor实例创建成功。
2025-07-06 19:26:49,772 - unicom_monitor.monitor - INFO - 启动联通手机号监控系统
2025-07-06 19:26:49,772 - unicom_monitor.monitor - INFO - 配置信息:
2025-07-06 19:26:49,772 - unicom_monitor.monitor - INFO -   - 请求间隔: 20 秒
2025-07-06 19:26:49,772 - unicom_monitor.monitor - INFO -   - 最大重试次数: 3
2025-07-06 19:26:49,772 - unicom_monitor.monitor - INFO -   - 数据存储: MySQL (192.168.0.115:3306)
2025-07-06 19:26:49,772 - unicom_monitor.monitor - INFO -   - 钉钉通知: 已配置
2025-07-06 19:26:49,772 - unicom_monitor.monitor - INFO - 测试钉钉连接...
2025-07-06 19:26:49,885 - unicom_monitor.dingtalk_notifier - INFO - 钉钉消息发送成功
2025-07-06 19:26:49,885 - unicom_monitor.monitor - INFO - 钉钉连接测试成功
2025-07-06 19:26:49,901 - unicom_monitor.monitor - INFO - 历史统计: 已处理 38609 个号码，发现 35 个特殊号码
2025-07-06 19:26:49,901 - unicom_monitor.monitor - INFO - 开始监控循环
2025-07-06 19:26:49,901 - unicom_monitor.monitor - INFO - 监控系统已启动
2025-07-06 19:26:49,902 - unicom_monitor.api_client - INFO - 正在初始化获取UNICOM_TOKEN...
2025-07-06 19:26:49,903 - unicom_monitor.token_manager - INFO - 成功加载缓存Token，过期时间: 2025-07-06 21:16:40
2025-07-06 19:26:49,903 - unicom_monitor.monitor - INFO - 正在获取手机号码...
2025-07-06 19:26:50,379 - unicom_monitor.network_utils - ERROR - API请求 - 未知错误: API返回错误: M8 - 未知错误
2025-07-06 19:26:50,379 - unicom_monitor.network_utils - ERROR - API请求 - 所有重试都失败
2025-07-06 19:26:50,379 - unicom_monitor.api_client - ERROR - API请求最终失败: API返回错误: M8 - 未知错误
2025-07-06 19:26:50,379 - unicom_monitor.monitor - WARNING - 未获取到任何手机号码
2025-07-06 19:26:50,379 - unicom_monitor.monitor - INFO - 等待 20 秒后进行下次检查...
2025-07-06 19:27:04,873 - unicom_monitor.monitor - INFO - 接收到信号 2，准备停止监控...
2025-07-06 19:27:04,873 - unicom_monitor.monitor - INFO - 正在停止监控系统...
2025-07-06 19:27:10,380 - unicom_monitor.monitor - INFO - 监控循环结束
2025-07-06 19:27:10,380 - unicom_monitor.db_manager - INFO - 关闭所有数据库连接
2025-07-06 19:27:10,380 - unicom_monitor.data_storage - INFO - 数据库连接已关闭
2025-07-06 19:27:10,380 - unicom_monitor.monitor - INFO - 监控系统已停止
2025-07-06 19:27:10,381 - unicom_monitor.monitor - WARNING - 监控未在运行
2025-07-06 19:32:01,025 - root - INFO - 程序启动，开始解析参数...
2025-07-06 19:32:01,025 - root - INFO - --- 启动联通靓号搜索程序 (ABCDABCD模式) ---
2025-07-06 19:32:01,025 - root - INFO - 目标地区: 省份代码=17, 城市代码=166
2025-07-06 19:32:01,025 - root - INFO - 结果将保存到: found_numbers_search.txt
2025-07-06 19:32:01,025 - root - INFO - 每次搜索延迟: 20 秒
2025-07-06 19:32:01,025 - root - ERROR - 无法获取有效的API Token，搜索中止。
