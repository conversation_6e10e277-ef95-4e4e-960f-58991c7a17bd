{% extends "base.html" %}

{% block title %}日志查看 - 联通手机号监控系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-file-alt me-2"></i>
        系统日志查看
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary" onclick="refreshLogs()">
                <i class="fas fa-sync-alt me-1"></i>刷新日志
            </button>
            <button type="button" class="btn btn-outline-success" onclick="downloadLog()">
                <i class="fas fa-download me-1"></i>下载日志
            </button>
        </div>
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="autoRefresh" onchange="toggleAutoRefresh()">
            <label class="form-check-label" for="autoRefresh">
                自动刷新
            </label>
        </div>
    </div>
</div>

<!-- 日志文件选择和过滤器 -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    日志过滤器
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label for="logFile" class="form-label">日志文件</label>
                        <select class="form-select" id="logFile" onchange="loadLogFile()">
                            <option value="">选择日志文件</option>
                            {% for log_file in log_files %}
                            <option value="{{ log_file.name }}">
                                {{ log_file.name }} ({{ "%.1f"|format(log_file.size / 1024) }}KB)
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="logLevel" class="form-label">日志级别</label>
                        <select class="form-select" id="logLevel" onchange="filterLogs()">
                            <option value="">全部级别</option>
                            <option value="DEBUG">DEBUG</option>
                            <option value="INFO">INFO</option>
                            <option value="WARNING">WARNING</option>
                            <option value="ERROR">ERROR</option>
                            <option value="CRITICAL">CRITICAL</option>
                        </select>
                    </div>
                    
                    <div class="col-md-5">
                        <label for="searchKeyword" class="form-label">关键词搜索</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchKeyword" 
                                   placeholder="输入搜索关键词..." onkeyup="filterLogs()">
                            <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    日志信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">总行数:</small>
                        <div class="fw-bold" id="totalLines">-</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">显示行数:</small>
                        <div class="fw-bold" id="showingLines">-</div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <small class="text-muted">最后更新:</small>
                        <div class="fw-bold" id="lastUpdate">-</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志内容显示区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-terminal me-2"></i>
                    日志内容
                </h5>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-secondary" onclick="scrollToTop()">
                        <i class="fas fa-arrow-up"></i> 顶部
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="scrollToBottom()">
                        <i class="fas fa-arrow-down"></i> 底部
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="log-container" id="logContent">
                    <div class="p-4 text-center text-muted">
                        <i class="fas fa-file-alt fa-3x mb-3"></i>
                        <p>请选择日志文件查看内容</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志级别颜色说明 -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-palette me-2"></i>
                    日志级别说明
                </h6>
                <div class="row">
                    <div class="col-md-2">
                        <span class="badge bg-secondary">DEBUG</span> 调试信息
                    </div>
                    <div class="col-md-2">
                        <span class="badge bg-info">INFO</span> 一般信息
                    </div>
                    <div class="col-md-2">
                        <span class="badge bg-warning">WARNING</span> 警告信息
                    </div>
                    <div class="col-md-2">
                        <span class="badge bg-danger">ERROR</span> 错误信息
                    </div>
                    <div class="col-md-2">
                        <span class="badge bg-dark">CRITICAL</span> 严重错误
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .log-container {
        height: 600px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;
    }
    
    .log-line {
        padding: 2px 8px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }
    
    .log-line:hover {
        background-color: rgba(255,255,255,0.05);
    }
    
    .log-debug { color: #6c757d; }
    .log-info { color: #17a2b8; }
    .log-warning { color: #ffc107; }
    .log-error { color: #dc3545; }
    .log-critical { color: #6f42c1; background-color: rgba(111, 66, 193, 0.1); }
    
    .log-timestamp { color: #28a745; }
    .log-module { color: #fd7e14; }
    .log-level { font-weight: bold; }
    .log-message { color: #e9ecef; }
    
    .highlight {
        background-color: #ffc107;
        color: #212529;
        padding: 1px 2px;
        border-radius: 2px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let autoRefreshInterval = null;
    let currentLogFile = '';
    let originalLogContent = '';
    
    // 加载日志文件
    async function loadLogFile() {
        const logFile = document.getElementById('logFile').value;
        if (!logFile) {
            document.getElementById('logContent').innerHTML = `
                <div class="p-4 text-center text-muted">
                    <i class="fas fa-file-alt fa-3x mb-3"></i>
                    <p>请选择日志文件查看内容</p>
                </div>
            `;
            return;
        }
        
        currentLogFile = logFile;
        
        try {
            document.getElementById('logContent').innerHTML = `
                <div class="p-4 text-center">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>正在加载日志文件...</p>
                </div>
            `;
            
            const result = await apiRequest(`/api/logs/${logFile}`);
            
            if (result.success) {
                originalLogContent = result.data.content;
                displayLogContent(originalLogContent);
                
                // 更新日志信息
                document.getElementById('totalLines').textContent = result.data.total_lines;
                document.getElementById('showingLines').textContent = result.data.showing_lines;
                document.getElementById('lastUpdate').textContent = new Date().toLocaleString('zh-CN');
                
                // 自动滚动到底部
                scrollToBottom();
            } else {
                document.getElementById('logContent').innerHTML = `
                    <div class="p-4 text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <p>加载日志文件失败: ${result.message}</p>
                    </div>
                `;
            }
        } catch (error) {
            document.getElementById('logContent').innerHTML = `
                <div class="p-4 text-center text-danger">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                    <p>加载日志文件时发生错误</p>
                </div>
            `;
        }
    }
    
    // 显示日志内容
    function displayLogContent(content) {
        const lines = content.split('\n');
        const logContainer = document.getElementById('logContent');
        
        let html = '';
        lines.forEach((line, index) => {
            if (line.trim()) {
                const formattedLine = formatLogLine(line);
                html += `<div class="log-line" data-line="${index}">${formattedLine}</div>`;
            }
        });
        
        logContainer.innerHTML = html || '<div class="p-4 text-center text-muted">日志文件为空</div>';
    }
    
    // 格式化日志行
    function formatLogLine(line) {
        // 匹配日志格式: 时间戳 - 模块 - 级别 - 消息
        const logPattern = /^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - ([^-]+) - (\w+) - (.+)$/;
        const match = line.match(logPattern);
        
        if (match) {
            const [, timestamp, module, level, message] = match;
            const levelClass = `log-${level.toLowerCase()}`;
            
            return `
                <span class="log-timestamp">${timestamp}</span> - 
                <span class="log-module">${module}</span> - 
                <span class="log-level ${levelClass}">${level}</span> - 
                <span class="log-message">${escapeHtml(message)}</span>
            `;
        } else {
            return `<span class="log-message">${escapeHtml(line)}</span>`;
        }
    }
    
    // 过滤日志
    function filterLogs() {
        if (!originalLogContent) return;
        
        const level = document.getElementById('logLevel').value;
        const keyword = document.getElementById('searchKeyword').value.toLowerCase();
        
        let filteredContent = originalLogContent;
        
        // 按级别过滤
        if (level) {
            const lines = filteredContent.split('\n');
            const filteredLines = lines.filter(line => 
                line.includes(` - ${level} - `) || !line.match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)
            );
            filteredContent = filteredLines.join('\n');
        }
        
        // 按关键词过滤
        if (keyword) {
            const lines = filteredContent.split('\n');
            const filteredLines = lines.filter(line => 
                line.toLowerCase().includes(keyword)
            );
            filteredContent = filteredLines.join('\n');
        }
        
        displayLogContent(filteredContent);
        
        // 高亮关键词
        if (keyword) {
            highlightKeyword(keyword);
        }
    }
    
    // 高亮关键词
    function highlightKeyword(keyword) {
        const logContainer = document.getElementById('logContent');
        const lines = logContainer.querySelectorAll('.log-line');
        
        lines.forEach(line => {
            const html = line.innerHTML;
            const regex = new RegExp(`(${escapeRegex(keyword)})`, 'gi');
            line.innerHTML = html.replace(regex, '<span class="highlight">$1</span>');
        });
    }
    
    // 清除搜索
    function clearSearch() {
        document.getElementById('searchKeyword').value = '';
        document.getElementById('logLevel').value = '';
        filterLogs();
    }
    
    // 刷新日志
    function refreshLogs() {
        if (currentLogFile) {
            loadLogFile();
        }
    }
    
    // 下载日志
    function downloadLog() {
        if (!currentLogFile) {
            showNotification('请先选择日志文件', 'warning');
            return;
        }
        
        const link = document.createElement('a');
        link.href = `/api/logs/${currentLogFile}`;
        link.download = currentLogFile;
        link.click();
    }
    
    // 切换自动刷新
    function toggleAutoRefresh() {
        const autoRefresh = document.getElementById('autoRefresh').checked;
        
        if (autoRefresh) {
            autoRefreshInterval = setInterval(refreshLogs, 10000); // 每10秒刷新
            showNotification('已启用自动刷新', 'info');
        } else {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
            showNotification('已关闭自动刷新', 'info');
        }
    }
    
    // 滚动到顶部
    function scrollToTop() {
        document.getElementById('logContent').scrollTop = 0;
    }
    
    // 滚动到底部
    function scrollToBottom() {
        const container = document.getElementById('logContent');
        container.scrollTop = container.scrollHeight;
    }
    
    // 工具函数
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    function escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    // 页面卸载时清除定时器
    window.addEventListener('beforeunload', function() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    });
</script>
{% endblock %}
