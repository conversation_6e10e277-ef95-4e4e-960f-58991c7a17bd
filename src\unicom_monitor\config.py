"""
配置管理模块

负责加载和管理应用程序配置。
"""

import os
from typing import Dict, Any
from dotenv import load_dotenv


class Config:
    """配置管理类"""
    
    def __init__(self, config_file: str = "config.env") -> None:
        """
        初始化配置
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            load_dotenv(self.config_file)
        
        # 联通API配置
        self.unicom_api_url = os.getenv("UNICOM_API_URL",
            "https://card.10010.com/NumDockerApp/NumberCenter/qryNum/newRecToken")
        self.ecs_token = os.getenv("ECS_TOKEN", "")
        self.province_code = os.getenv("PROVINCE_CODE", "17")
        self.city_code = os.getenv("CITY_CODE", "166")
        self.num_class = os.getenv("NUM_CLASS", "1")
        self.amounts = os.getenv("AMOUNTS", "100")
        self.channel = os.getenv("CHANNEL", "B2C")
        self.chnl_type = os.getenv("CHNL_TYPE", "1")
        self.group_type = os.getenv("GROUP_TYPE", "1")
        self.call_type = os.getenv("CALL_TYPE", "1")
        self.monitor_purpose = os.getenv("MONITOR_PURPOSE", "1009")
        self.search_ratio = os.getenv("SEARCH_RATIO", "100")
        self.search_code = os.getenv("SEARCH_CODE", "02")
        self.search_type = os.getenv("SEARCH_TYPE", "1")
        self.select_rule = os.getenv("SELECT_RULE", "101,201,202,301,302")
        
        # 数据库配置
        self.db_host = os.getenv("DB_HOST", "localhost")
        self.db_port = int(os.getenv("DB_PORT", "3306"))
        self.db_user = os.getenv("DB_USER", "")
        self.db_password = os.getenv("DB_PASSWORD", "")
        self.db_name = os.getenv("DB_NAME", "")
        
        # 钉钉配置
        self.dingtalk_webhook = os.getenv("DINGTALK_WEBHOOK", "")
        self.dingtalk_secret = os.getenv("DINGTALK_SECRET", "")
        
        # 监控配置
        self.request_interval = int(os.getenv("REQUEST_INTERVAL", "5"))
        self.max_retries = int(os.getenv("MAX_RETRIES", "3"))
        self.retry_delay = int(os.getenv("RETRY_DELAY", "2"))
        
        # 日志配置
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_file = os.getenv("LOG_FILE", "logs/monitor.log")
        
        # 确保日志目录存在
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
    
    def get_api_params(self) -> Dict[str, Any]:
        """获取API请求参数"""
        return {
            "provinceCode": self.province_code,
            "cityCode": self.city_code,
            "numClass": self.num_class,
            "amounts": self.amounts,
            "channel": self.channel,
            "chnlType": self.chnl_type,
            "groupType": self.group_type,
            "callType": self.call_type,
            "monitorPurpose": self.monitor_purpose,
            "searchRatio": self.search_ratio,
            "searchValue": "",
            "searchCode": self.search_code,
            "jsonp": "callback",
            "searchType": self.search_type,
            "serialNumber": "",
            "selectRule": self.select_rule,
            "goodsId": "",
        }
    
    def get_request_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            "Host": "card.10010.com",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache",
            "Sec-Ch-Ua-Platform": '"Android"',
            "User-Agent": "Mozilla/5.0 (Linux; Android 15; PJE110 Build/TP1A.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/130.0.6723.58 Mobile Safari/537.36; unicom{version:android@12.0500,desmobile:0};devicetype{deviceBrand:OnePlus,deviceModel:PJE110};OSVersion/15;ltst;",
            "Sec-Ch-Ua": '"Chromium";v="130", "Android WebView";v="130", "Not?A_Brand";v="99"',
            "Sec-Ch-Ua-Mobile": "?1",
            "Accept": "*/*",
            "X-Requested-With": "com.sinovatech.unicom.ui",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "no-cors",
            "Sec-Fetch-Dest": "script",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        }
