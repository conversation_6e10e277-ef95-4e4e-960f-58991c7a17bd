"""
号码分析模块

负责识别和分析手机号码的特殊模式，包括3A、4A、回旋号、顺子等。
"""

import re
import logging
import requests
from typing import List, Dict, Set, Optional, Any
from dataclasses import dataclass
from enum import Enum


logger = logging.getLogger(__name__)


class NumberType(Enum):
    """号码类型枚举"""
    # 原有规则
    TAIL_3A = "尾号3A"
    FULL_4A = "全段4A"
    TAIL_DOUBLE_SPIRAL = "尾号双回旋"
    TAIL_TRIPLE_SPIRAL = "尾号三回旋"
    TAIL_4_SPIRAL = "尾号四回旋"
    TAIL_4_SEQUENCE = "尾号4顺子"
    FULL_5_SEQUENCE = "全段5顺子"

    # 新增规则
    AREA_CODE_EMBED = "区号嵌入"        # 如 132-0537-XXXX
    AABB_PATTERN = "AABB模式"          # 如 1388-8899
    ABAB_PATTERN = "ABAB模式"          # 如 1388-3883
    BIRTHDAY_PATTERN = "生日号码"       # 如 138-1201-XXXX (12月01日)
    LUCKY_PATTERN = "吉祥号码"         # 如 138-6666-8888
    MIRROR_PATTERN = "镜像号码"        # 如 138-1234-4321
    ASCENDING_PATTERN = "递增号码"      # 如 138-1234-5678
    DESCENDING_PATTERN = "递减号码"     # 如 138-8765-4321


@dataclass
class SpecialNumber:
    """特殊号码数据类"""
    number: str
    number_type: NumberType
    pattern: str
    description: str
    city_code: str = ""
    city_name: str = ""
    area_code: str = ""
    phone_attribution: Dict[str, Any] = None

    def __post_init__(self):
        if self.phone_attribution is None:
            self.phone_attribution = {}


class NumberAnalyzer:
    """号码分析器"""
    
    def __init__(self, city_manager=None) -> None:
        """
        初始化号码分析器

        Args:
            city_manager: 城市管理器
        """
        self.city_manager = city_manager
        self.processed_numbers: Set[str] = set()
        self.phone_attribution_cache = {}  # 手机号归属地缓存
        logger.info("号码分析器初始化完成")

    def get_phone_attribution(self, number: str) -> Dict[str, Any]:
        """
        获取手机号归属地信息

        Args:
            number: 手机号码

        Returns:
            归属地信息字典
        """
        if number in self.phone_attribution_cache:
            return self.phone_attribution_cache[number]

        try:
            # 使用免费的手机号归属地API
            url = f"https://tcc.taobao.com/cc/json/mobile_tel_segment.htm?tel={number[:7]}"
            response = requests.get(url, timeout=5)

            if response.status_code == 200:
                # 解析响应 (JSONP格式)
                text = response.text
                start = text.find('{')
                end = text.rfind('}') + 1
                if start >= 0 and end > start:
                    import json
                    data = json.loads(text[start:end])

                    attribution = {
                        "province": data.get("province", ""),
                        "city": data.get("city", ""),
                        "carrier": data.get("carrier", ""),
                        "area_code": data.get("areaVid", ""),
                        "zip_code": data.get("zipVid", "")
                    }

                    # 缓存结果
                    self.phone_attribution_cache[number] = attribution
                    return attribution
        except Exception as e:
            logger.debug(f"获取手机号归属地失败: {e}")

        # 返回空信息
        empty_attribution = {
            "province": "",
            "city": "",
            "carrier": "",
            "area_code": "",
            "zip_code": ""
        }
        self.phone_attribution_cache[number] = empty_attribution
        return empty_attribution

    def _is_tail_3a(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为尾号3A（如：1111、2222、3333等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是3A号码返回SpecialNumber对象，否则返回None
        """
        if len(number) < 4:
            return None
        
        tail_3 = number[-3:]
        # 检查后3位是否相同
        if tail_3[0] == tail_3[1] == tail_3[2]:
            pattern = tail_3[0] * 3
            return SpecialNumber(
                number=number,
                number_type=NumberType.TAIL_3A,
                pattern=pattern,
                description=f"尾号3A: {pattern}"
            )
        return None
    
    def _is_full_4a(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为全段4A（如：1111-1111-111、2222-2222-222等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是4A号码返回SpecialNumber对象，否则返回None
        """
        if len(number) != 11:
            return None
        
        # 检查是否有连续4个相同数字
        for i in range(len(number) - 3):
            if (number[i] == number[i+1] == number[i+2] == number[i+3] and 
                number[i].isdigit()):
                pattern = number[i] * 4
                return SpecialNumber(
                    number=number,
                    number_type=NumberType.FULL_4A,
                    pattern=pattern,
                    description=f"全段4A: {pattern}"
                )
        return None
    
    def _is_tail_4_spiral(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为尾号四回旋（如：12341234、56785678等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是四回旋号码返回SpecialNumber对象，否则返回None
        """
        if len(number) < 8:
            return None
        
        tail_8 = number[-8:]
        # 检查ABCDABCD模式，并排除AAAAAAAA模式
        if (tail_8[0:4] == tail_8[4:8] and 
            len(set(tail_8[0:4])) > 1):
            pattern = tail_8
            return SpecialNumber(
                number=number,
                number_type=NumberType.TAIL_4_SPIRAL,
                pattern=pattern,
                description=f"尾号四回旋: {pattern}"
            )
        return None
    
    def _is_tail_triple_spiral(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为尾号三回旋（如：401401、824824等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是三回旋号码返回SpecialNumber对象，否则返回None
        """
        if len(number) < 6:
            return None
        
        tail_6 = number[-6:]
        # 检查ABCABC模式，并排除AAAAAA模式
        if (tail_6[0:3] == tail_6[3:6] and 
            (tail_6[0] != tail_6[1] or tail_6[1] != tail_6[2])):
            pattern = tail_6
            return SpecialNumber(
                number=number,
                number_type=NumberType.TAIL_TRIPLE_SPIRAL,
                pattern=pattern,
                description=f"尾号三回旋: {pattern}"
            )
        return None

    def _is_tail_double_spiral(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为尾号双回旋（如：1212、3434、5656等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是双回旋号码返回SpecialNumber对象，否则返回None
        """
        if len(number) < 4:
            return None
        
        tail_4 = number[-4:]
        # 检查ABAB模式
        if tail_4[0] == tail_4[2] and tail_4[1] == tail_4[3] and tail_4[0] != tail_4[1]:
            pattern = tail_4
            return SpecialNumber(
                number=number,
                number_type=NumberType.TAIL_DOUBLE_SPIRAL,
                pattern=pattern,
                description=f"尾号双回旋: {pattern}"
            )
        return None
    
    def _is_tail_4_sequence(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为尾号4顺子（如：1234、5678、9876等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是4顺子返回SpecialNumber对象，否则返回None
        """
        if len(number) < 4:
            return None
        
        tail_4 = number[-4:]
        
        # 检查递增顺子
        is_ascending = True
        for i in range(3):
            if int(tail_4[i+1]) != int(tail_4[i]) + 1:
                is_ascending = False
                break
        
        # 检查递减顺子
        is_descending = True
        for i in range(3):
            if int(tail_4[i+1]) != int(tail_4[i]) - 1:
                is_descending = False
                break
        
        if is_ascending or is_descending:
            sequence_type = "递增" if is_ascending else "递减"
            return SpecialNumber(
                number=number,
                number_type=NumberType.TAIL_4_SEQUENCE,
                pattern=tail_4,
                description=f"尾号4顺子({sequence_type}): {tail_4}"
            )
        return None
    
    def _is_full_5_sequence(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为全段5顺子（如：12345、56789、98765等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是5顺子返回SpecialNumber对象，否则返回None
        """
        if len(number) != 11:
            return None
        
        # 检查是否有连续5个递增或递减的数字
        for i in range(len(number) - 4):
            segment = number[i:i+5]
            
            # 检查递增顺子
            is_ascending = True
            for j in range(4):
                if int(segment[j+1]) != int(segment[j]) + 1:
                    is_ascending = False
                    break
            
            # 检查递减顺子
            is_descending = True
            for j in range(4):
                if int(segment[j+1]) != int(segment[j]) - 1:
                    is_descending = False
                    break
            
            if is_ascending or is_descending:
                sequence_type = "递增" if is_ascending else "递减"
                return SpecialNumber(
                    number=number,
                    number_type=NumberType.FULL_5_SEQUENCE,
                    pattern=segment,
                    description=f"全段5顺子({sequence_type}): {segment}"
                )
        return None
    
    def analyze_number(self, number: str, city_code: str = "") -> List[SpecialNumber]:
        """
        分析单个号码

        Args:
            number: 手机号码
            city_code: 城市代码

        Returns:
            特殊号码列表（一个号码可能匹配多种模式）
        """
        special_numbers = []

        # 获取城市配置
        city_config = None
        if self.city_manager and city_code:
            city_config = self.city_manager.get_city_config(city_code)

        # 获取手机号归属地信息
        phone_attribution = self.get_phone_attribution(number)

        # 按优先级检查各种模式（优先级从高到低）
        # 1. 全段4A（最高优先级）
        if not city_config or city_config.is_rule_enabled("leopard"):
            result = self._is_full_4a(number)
            if result:
                self._enrich_special_number(result, city_code, phone_attribution)
                special_numbers.append(result)
                return special_numbers

        # 2. 全段5顺子
        result = self._is_full_5_sequence(number)
        if result:
            special_numbers.append(result)
            return special_numbers

        # 3. 尾号四回旋
        result = self._is_tail_4_spiral(number)
        if result:
            special_numbers.append(result)
            return special_numbers

        # 4. 尾号三回旋
        result = self._is_tail_triple_spiral(number)
        if result:
            special_numbers.append(result)
            return special_numbers

        # 5. 尾号3A
        result = self._is_tail_3a(number)
        if result:
            special_numbers.append(result)
            return special_numbers

        # 6. 尾号双回旋
        result = self._is_tail_double_spiral(number)
        if result:
            special_numbers.append(result)
            return special_numbers

        # 7. 尾号4顺子（最低优先级）
        result = self._is_tail_4_sequence(number)
        if result:
            special_numbers.append(result)
            return special_numbers
        
        return special_numbers
    
    def analyze_numbers(self, numbers: List[str], city_code: str = "") -> List[SpecialNumber]:
        """
        批量分析号码
        
        Args:
            numbers: 手机号码列表
            
        Returns:
            特殊号码列表
        """
        special_numbers = []
        new_numbers = []
        
        for number in numbers:
            if number not in self.processed_numbers:
                new_numbers.append(number)
                self.processed_numbers.add(number)
        
        logger.info(f"分析 {len(new_numbers)} 个新号码")
        
        for number in new_numbers:
            results = self.analyze_number(number, city_code)
            special_numbers.extend(results)
        
        if special_numbers:
            logger.info(f"发现 {len(special_numbers)} 个特殊号码")
            for special in special_numbers:
                logger.info(f"  {special.description}: {special.number}")
        
        return special_numbers
    
    def get_processed_count(self) -> int:
        """获取已处理号码数量"""
        return len(self.processed_numbers)
    
    def clear_processed(self) -> None:
        """清空已处理号码记录"""
        self.processed_numbers.clear()
        logger.info("已清空处理记录")

    def _is_area_code_embed(self, number: str, area_code: str) -> Optional[SpecialNumber]:
        """
        检查是否为区号嵌入模式（如：132-0537-XXXX）

        Args:
            number: 手机号码
            area_code: 区号（如0537）

        Returns:
            如果匹配返回SpecialNumber对象，否则返回None
        """
        if not area_code or len(number) != 11:
            return None

        # 去掉区号前的0
        area_digits = area_code.lstrip('0')
        if len(area_digits) < 3:
            return None

        # 检查第4-6位或第4-7位是否包含区号
        for start_pos in [3, 4]:  # 从第4位或第5位开始
            if start_pos + len(area_digits) <= 11:
                segment = number[start_pos:start_pos + len(area_digits)]
                if segment == area_digits:
                    return SpecialNumber(
                        number=number,
                        number_type=NumberType.AREA_CODE_EMBED,
                        pattern=f"{area_code}嵌入",
                        description=f"区号{area_code}嵌入在第{start_pos+1}位: {number[:start_pos]}-{segment}-{number[start_pos+len(area_digits):]}"
                    )

        return None

    def _is_aabb_pattern(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为AABB模式（如：1388-8899）

        Args:
            number: 手机号码

        Returns:
            如果匹配返回SpecialNumber对象，否则返回None
        """
        if len(number) != 11:
            return None

        # 检查后4位是否为AABB模式
        tail_4 = number[-4:]
        if tail_4[0] == tail_4[1] and tail_4[2] == tail_4[3] and tail_4[0] != tail_4[2]:
            return SpecialNumber(
                number=number,
                number_type=NumberType.AABB_PATTERN,
                pattern=f"{tail_4[0]}{tail_4[0]}{tail_4[2]}{tail_4[2]}",
                description=f"尾号AABB模式: {tail_4}"
            )

        # 检查中间4位是否为AABB模式
        middle_4 = number[3:7]
        if middle_4[0] == middle_4[1] and middle_4[2] == middle_4[3] and middle_4[0] != middle_4[2]:
            return SpecialNumber(
                number=number,
                number_type=NumberType.AABB_PATTERN,
                pattern=f"{middle_4[0]}{middle_4[0]}{middle_4[2]}{middle_4[2]}",
                description=f"中段AABB模式: {middle_4}"
            )

        return None

    def _enrich_special_number(self, special_number: SpecialNumber, city_code: str, phone_attribution: Dict[str, Any]) -> None:
        """
        丰富特殊号码信息

        Args:
            special_number: 特殊号码对象
            city_code: 城市代码
            phone_attribution: 手机号归属地信息
        """
        # 设置城市信息
        if self.city_manager and city_code:
            city_config = self.city_manager.get_city_config(city_code)
            if city_config:
                special_number.city_code = city_code
                special_number.city_name = city_config.city_name
                special_number.area_code = city_config.area_code

        # 设置归属地信息
        special_number.phone_attribution = phone_attribution

    def _is_abab_pattern(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为ABAB模式（如：1388-3883）

        Args:
            number: 手机号码

        Returns:
            如果匹配返回SpecialNumber对象，否则返回None
        """
        if len(number) != 11:
            return None

        # 检查后4位是否为ABAB模式
        tail_4 = number[-4:]
        if tail_4[0] == tail_4[2] and tail_4[1] == tail_4[3] and tail_4[0] != tail_4[1]:
            return SpecialNumber(
                number=number,
                number_type=NumberType.ABAB_PATTERN,
                pattern=f"{tail_4[0]}{tail_4[1]}{tail_4[0]}{tail_4[1]}",
                description=f"尾号ABAB模式: {tail_4}"
            )

        return None

    def _is_mirror_pattern(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为镜像号码（如：138-1234-4321）

        Args:
            number: 手机号码

        Returns:
            如果匹配返回SpecialNumber对象，否则返回None
        """
        if len(number) != 11:
            return None

        # 检查后4位是否为镜像
        tail_4 = number[-4:]
        if tail_4 == tail_4[::-1]:
            return SpecialNumber(
                number=number,
                number_type=NumberType.MIRROR_PATTERN,
                pattern=tail_4,
                description=f"尾号镜像: {tail_4}"
            )

        # 检查后6位是否为镜像
        tail_6 = number[-6:]
        if tail_6 == tail_6[::-1]:
            return SpecialNumber(
                number=number,
                number_type=NumberType.MIRROR_PATTERN,
                pattern=tail_6,
                description=f"尾号6位镜像: {tail_6}"
            )

        return None
