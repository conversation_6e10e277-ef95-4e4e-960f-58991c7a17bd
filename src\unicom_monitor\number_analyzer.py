"""
号码分析模块

负责识别和分析手机号码的特殊模式，包括3A、4A、回旋号、顺子等。
"""

import re
import logging
from typing import List, Dict, Set, Optional
from dataclasses import dataclass
from enum import Enum


logger = logging.getLogger(__name__)


class NumberType(Enum):
    """号码类型枚举"""
    TAIL_3A = "尾号3A"
    FULL_4A = "全段4A"
    TAIL_DOUBLE_SPIRAL = "尾号双回旋"
    TAIL_TRIPLE_SPIRAL = "尾号三回旋"
    TAIL_4_SPIRAL = "尾号四回旋"
    TAIL_4_SEQUENCE = "尾号4顺子"
    FULL_5_SEQUENCE = "全段5顺子"


@dataclass
class SpecialNumber:
    """特殊号码数据类"""
    number: str
    number_type: NumberType
    pattern: str
    description: str


class NumberAnalyzer:
    """号码分析器"""
    
    def __init__(self) -> None:
        """初始化号码分析器"""
        self.processed_numbers: Set[str] = set()
    
    def _is_tail_3a(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为尾号3A（如：1111、2222、3333等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是3A号码返回SpecialNumber对象，否则返回None
        """
        if len(number) < 4:
            return None
        
        tail_3 = number[-3:]
        # 检查后3位是否相同
        if tail_3[0] == tail_3[1] == tail_3[2]:
            pattern = tail_3[0] * 3
            return SpecialNumber(
                number=number,
                number_type=NumberType.TAIL_3A,
                pattern=pattern,
                description=f"尾号3A: {pattern}"
            )
        return None
    
    def _is_full_4a(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为全段4A（如：1111-1111-111、2222-2222-222等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是4A号码返回SpecialNumber对象，否则返回None
        """
        if len(number) != 11:
            return None
        
        # 检查是否有连续4个相同数字
        for i in range(len(number) - 3):
            if (number[i] == number[i+1] == number[i+2] == number[i+3] and 
                number[i].isdigit()):
                pattern = number[i] * 4
                return SpecialNumber(
                    number=number,
                    number_type=NumberType.FULL_4A,
                    pattern=pattern,
                    description=f"全段4A: {pattern}"
                )
        return None
    
    def _is_tail_4_spiral(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为尾号四回旋（如：12341234、56785678等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是四回旋号码返回SpecialNumber对象，否则返回None
        """
        if len(number) < 8:
            return None
        
        tail_8 = number[-8:]
        # 检查ABCDABCD模式，并排除AAAAAAAA模式
        if (tail_8[0:4] == tail_8[4:8] and 
            len(set(tail_8[0:4])) > 1):
            pattern = tail_8
            return SpecialNumber(
                number=number,
                number_type=NumberType.TAIL_4_SPIRAL,
                pattern=pattern,
                description=f"尾号四回旋: {pattern}"
            )
        return None
    
    def _is_tail_triple_spiral(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为尾号三回旋（如：401401、824824等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是三回旋号码返回SpecialNumber对象，否则返回None
        """
        if len(number) < 6:
            return None
        
        tail_6 = number[-6:]
        # 检查ABCABC模式，并排除AAAAAA模式
        if (tail_6[0:3] == tail_6[3:6] and 
            (tail_6[0] != tail_6[1] or tail_6[1] != tail_6[2])):
            pattern = tail_6
            return SpecialNumber(
                number=number,
                number_type=NumberType.TAIL_TRIPLE_SPIRAL,
                pattern=pattern,
                description=f"尾号三回旋: {pattern}"
            )
        return None

    def _is_tail_double_spiral(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为尾号双回旋（如：1212、3434、5656等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是双回旋号码返回SpecialNumber对象，否则返回None
        """
        if len(number) < 4:
            return None
        
        tail_4 = number[-4:]
        # 检查ABAB模式
        if tail_4[0] == tail_4[2] and tail_4[1] == tail_4[3] and tail_4[0] != tail_4[1]:
            pattern = tail_4
            return SpecialNumber(
                number=number,
                number_type=NumberType.TAIL_DOUBLE_SPIRAL,
                pattern=pattern,
                description=f"尾号双回旋: {pattern}"
            )
        return None
    
    def _is_tail_4_sequence(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为尾号4顺子（如：1234、5678、9876等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是4顺子返回SpecialNumber对象，否则返回None
        """
        if len(number) < 4:
            return None
        
        tail_4 = number[-4:]
        
        # 检查递增顺子
        is_ascending = True
        for i in range(3):
            if int(tail_4[i+1]) != int(tail_4[i]) + 1:
                is_ascending = False
                break
        
        # 检查递减顺子
        is_descending = True
        for i in range(3):
            if int(tail_4[i+1]) != int(tail_4[i]) - 1:
                is_descending = False
                break
        
        if is_ascending or is_descending:
            sequence_type = "递增" if is_ascending else "递减"
            return SpecialNumber(
                number=number,
                number_type=NumberType.TAIL_4_SEQUENCE,
                pattern=tail_4,
                description=f"尾号4顺子({sequence_type}): {tail_4}"
            )
        return None
    
    def _is_full_5_sequence(self, number: str) -> Optional[SpecialNumber]:
        """
        检查是否为全段5顺子（如：12345、56789、98765等）
        
        Args:
            number: 手机号码
            
        Returns:
            如果是5顺子返回SpecialNumber对象，否则返回None
        """
        if len(number) != 11:
            return None
        
        # 检查是否有连续5个递增或递减的数字
        for i in range(len(number) - 4):
            segment = number[i:i+5]
            
            # 检查递增顺子
            is_ascending = True
            for j in range(4):
                if int(segment[j+1]) != int(segment[j]) + 1:
                    is_ascending = False
                    break
            
            # 检查递减顺子
            is_descending = True
            for j in range(4):
                if int(segment[j+1]) != int(segment[j]) - 1:
                    is_descending = False
                    break
            
            if is_ascending or is_descending:
                sequence_type = "递增" if is_ascending else "递减"
                return SpecialNumber(
                    number=number,
                    number_type=NumberType.FULL_5_SEQUENCE,
                    pattern=segment,
                    description=f"全段5顺子({sequence_type}): {segment}"
                )
        return None
    
    def analyze_number(self, number: str) -> List[SpecialNumber]:
        """
        分析单个号码
        
        Args:
            number: 手机号码
            
        Returns:
            特殊号码列表（一个号码可能匹配多种模式）
        """
        special_numbers = []
        
        # 按优先级检查各种模式（优先级从高到低）
        # 1. 全段4A（最高优先级）
        result = self._is_full_4a(number)
        if result:
            special_numbers.append(result)
            return special_numbers

        # 2. 全段5顺子
        result = self._is_full_5_sequence(number)
        if result:
            special_numbers.append(result)
            return special_numbers

        # 3. 尾号四回旋
        result = self._is_tail_4_spiral(number)
        if result:
            special_numbers.append(result)
            return special_numbers

        # 4. 尾号三回旋
        result = self._is_tail_triple_spiral(number)
        if result:
            special_numbers.append(result)
            return special_numbers

        # 5. 尾号3A
        result = self._is_tail_3a(number)
        if result:
            special_numbers.append(result)
            return special_numbers

        # 6. 尾号双回旋
        result = self._is_tail_double_spiral(number)
        if result:
            special_numbers.append(result)
            return special_numbers

        # 7. 尾号4顺子（最低优先级）
        result = self._is_tail_4_sequence(number)
        if result:
            special_numbers.append(result)
            return special_numbers
        
        return special_numbers
    
    def analyze_numbers(self, numbers: List[str]) -> List[SpecialNumber]:
        """
        批量分析号码
        
        Args:
            numbers: 手机号码列表
            
        Returns:
            特殊号码列表
        """
        special_numbers = []
        new_numbers = []
        
        for number in numbers:
            if number not in self.processed_numbers:
                new_numbers.append(number)
                self.processed_numbers.add(number)
        
        logger.info(f"分析 {len(new_numbers)} 个新号码")
        
        for number in new_numbers:
            results = self.analyze_number(number)
            special_numbers.extend(results)
        
        if special_numbers:
            logger.info(f"发现 {len(special_numbers)} 个特殊号码")
            for special in special_numbers:
                logger.info(f"  {special.description}: {special.number}")
        
        return special_numbers
    
    def get_processed_count(self) -> int:
        """获取已处理号码数量"""
        return len(self.processed_numbers)
    
    def clear_processed(self) -> None:
        """清空已处理号码记录"""
        self.processed_numbers.clear()
        logger.info("已清空处理记录")
