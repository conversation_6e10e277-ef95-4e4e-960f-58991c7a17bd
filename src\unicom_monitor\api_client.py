"""
联通API客户端模块

负责与联通手机号查询API进行交互，包括请求发送、响应解析和错误处理。
"""

import re
import json
import time
import random
import logging
from typing import List, Dict, Any, Optional
import requests
from urllib.parse import urlencode

from .config import Config
from .token_manager import TokenManager
from .network_utils import NetworkRetryMixin
from .db_manager import DatabaseManager


logger = logging.getLogger(__name__)


class UnicomAPIClient(NetworkRetryMixin):
    """联通API客户端，具备自动刷新Token功能"""
    
    def __init__(self, config: Config, db_manager: Optional[DatabaseManager] = None):
        """
        初始化API客户端

        Args:
            config: 配置对象
            db_manager: 数据库管理器
        """
        super().__init__()
        self.config = config
        self.session = requests.Session()
        self.session.headers.update(config.get_request_headers())
        self.token_manager = TokenManager(config, db_manager)
        self.unicom_token: Optional[str] = None
        self._token_refresh_attempts = 0

    def _initialize_token(self) -> bool:
        """
        初始化获取Token，在客户端首次使用时调用
        
        Returns:
            True如果成功获取Token，否则False
        """
        logger.info("正在初始化获取UNICOM_TOKEN...")
        try:
            self.unicom_token = self.token_manager.get_valid_token()
            if self.unicom_token:
                self._token_refresh_attempts = 0
                return True
            else:
                logger.error("无法获取有效Token")
                return False
        except Exception as e:
            logger.error(f"初始化Token失败: {e}")
            self.unicom_token = None
            return False
            
    def _refresh_token(self) -> bool:
        """
        刷新Token
        
        Returns:
            True如果成功刷新Token，否则False
        """
        self._token_refresh_attempts += 1
        if self._token_refresh_attempts > self.config.max_retries:
            logger.error("Token刷新次数过多，请检查ECS_TOKEN是否已失效")
            return False
            
        logger.warning("UNICOM_TOKEN已失效或未初始化，正在尝试刷新...")
        try:
            if self.token_manager.refresh_token():
                self.unicom_token = self.token_manager.get_valid_token()
                if self.unicom_token:
                    self._token_refresh_attempts = 0
                    logger.info("UNICOM_TOKEN已成功刷新")
                    return True

            logger.error("Token刷新失败")
            return False
        except Exception as e:
            logger.error(f"刷新Token失败: {e}")
            self.unicom_token = None
            return False

    def _generate_callback_name(self) -> str:
        """生成JSONP回调函数名"""
        timestamp = int(time.time() * 1000)
        random_num = random.randint(10000, 99999)
        return f"jsonp_{timestamp}_{random_num}"
    
    def _parse_jsonp_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """
        解析JSONP响应
        
        Args:
            response_text: 响应文本
            
        Returns:
            解析后的JSON数据，解析失败返回None
        """
        try:
            # 使用正则表达式提取JSON部分，支持分号结尾
            pattern = r'jsonp_\d+_\d+\((.*)\);?$'
            match = re.search(pattern, response_text, re.DOTALL)
            if match:
                json_str = match.group(1)
                return json.loads(json_str)
            else:
                logger.error(f"无法从响应中提取JSON数据: {response_text[:200]}...")
                return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"解析JSONP响应时发生错误: {e}")
            return None
    
    def _extract_phone_numbers(self, data: Dict[str, Any]) -> List[str]:
        """
        从API响应中提取手机号码
        
        Args:
            data: API响应数据
            
        Returns:
            手机号码列表
        """
        phone_numbers = []
        
        if "numArray" not in data:
            logger.warning("响应数据中没有找到numArray字段")
            return phone_numbers
        
        num_array = data["numArray"]
        split_len = int(data.get("splitLen", 17))
        
        # 每17个元素为一组，第一个元素是手机号码
        for i in range(0, len(num_array), split_len):
            if i < len(num_array):
                phone_number = num_array[i]
                if phone_number and len(phone_number) == 11:
                    phone_numbers.append(phone_number)
        
        logger.info(f"从API响应中提取到 {len(phone_numbers)} 个手机号码")
        return phone_numbers
    
    def get_numbers(self) -> List[str]:
        """
        获取手机号码列表，包含Token刷新和重试逻辑

        Returns:
            手机号码列表
        """
        if not self.unicom_token:
            if not self._initialize_token():
                logger.error("无法启动监控，因为首次Token获取失败")
                return []

        def _make_api_request():
            callback_name = self._generate_callback_name()
            params = self.config.get_api_params()
            params["callback"] = callback_name
            params["token"] = self.unicom_token

            response = self.session.get(self.config.unicom_api_url, params=params, timeout=30)
            response.raise_for_status()

            data = self._parse_jsonp_response(response.text)
            if data is None:
                raise ValueError("无法解析API响应")

            rsp_code = data.get("rspCode")
            if rsp_code == "0000":
                phone_numbers = self._extract_phone_numbers(data)
                logger.info(f"成功获取 {len(phone_numbers)} 个手机号码")
                return phone_numbers
            elif rsp_code == "M13":
                logger.warning("API返回M13错误，Token已失效")
                if self._refresh_token():
                    # Token刷新成功，更新参数并重试
                    params["token"] = self.unicom_token
                    response = self.session.get(self.config.unicom_api_url, params=params, timeout=30)
                    response.raise_for_status()

                    data = self._parse_jsonp_response(response.text)
                    if data and data.get("rspCode") == "0000":
                        phone_numbers = self._extract_phone_numbers(data)
                        logger.info(f"Token刷新后成功获取 {len(phone_numbers)} 个手机号码")
                        return phone_numbers
                    else:
                        raise ValueError("Token刷新后API仍然失败")
                else:
                    raise ValueError("Token刷新失败，无法继续获取号码")
            else:
                rsp_desc = data.get("rspDesc", "未知错误")
                raise ValueError(f"API返回错误: {rsp_code} - {rsp_desc}")

        try:
            return self.retry_with_backoff(_make_api_request, "API请求")
        except Exception as e:
            logger.error(f"API请求最终失败: {e}")
            return []
    
    def search_numbers_by_keyword(self, keyword: str) -> List[str]:
        """
        根据关键词搜索手机号码

        Args:
            keyword: 搜索关键词 (例如 "1234")

        Returns:
            手机号码列表
        """
        if not self.unicom_token:
            logger.error("无法执行搜索，因为Token无效")
            return []

        def _make_search_request():
            params = {
                "provinceCode": self.config.province_code,
                "cityCode": self.config.city_code,
                "numClass": self.config.num_class,
                "token": self.unicom_token,
                "amounts": str(self.config.amounts),
                "channel": self.config.channel,
                "chnlType": self.config.chnl_type,
                "groupType": self.config.group_type,
                "callType": self.config.call_type,
                "monitorPurpose": self.config.monitor_purpose,
                "searchRatio": str(self.config.search_ratio),
                "searchValue": keyword,
                "searchCode": "01",  # 01 代表关键词搜索
                "searchType": self.config.search_type,
                "selectRule": "0",  # 关键词搜索时，规则为0
                "callback": f"jsonp_{int(time.time() * 1000)}_{random.randint(10000, 99999)}"
            }

            response = self.session.get(self.config.unicom_api_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = self._parse_jsonp_response(response.text)
            if not data:
                raise ValueError("无法解析API响应")

            rsp_code = data.get("rspCode")
            
            if rsp_code == "0000":
                phone_numbers = self._extract_phone_numbers(data)
                logger.info(f"关键词 '{keyword}' 搜索成功，获取 {len(phone_numbers)} 个号码")
                return phone_numbers
            elif rsp_code == "M13":
                logger.warning("API返回M13错误，Token已失效")
                if self._refresh_token():
                    params["token"] = self.unicom_token
                    response = self.session.get(self.config.unicom_api_url, params=params, timeout=30)
                    response.raise_for_status()
                    
                    data = self._parse_jsonp_response(response.text)
                    if data and data.get("rspCode") == "0000":
                        phone_numbers = self._extract_phone_numbers(data)
                        logger.info(f"Token刷新后，关键词 '{keyword}' 搜索成功，获取 {len(phone_numbers)} 个号码")
                        return phone_numbers
                    else:
                        raise ValueError("Token刷新后API请求仍然失败")
                else:
                    raise ValueError("Token刷新失败，无法继续搜索")
            else:
                rsp_desc = data.get("rspDesc", "未知错误")
                raise ValueError(f"API返回错误: {rsp_code} - {rsp_desc}")

        try:
            return self.retry_with_backoff(_make_search_request, f"关键词 '{keyword}' 搜索")
        except Exception as e:
            logger.error(f"关键词 '{keyword}' 搜索最终失败: {e}")
            return []

    def close(self) -> None:
        """关闭会话"""
        self.session.close()
