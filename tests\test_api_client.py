"""
API客户端测试模块
"""

import pytest
import json
import sys
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from unicom_monitor.api_client import UnicomAPIClient
from unicom_monitor.config import Config


class TestUnicomAPIClient:
    """API客户端测试类"""
    
    def setup_method(self):
        """测试方法设置"""
        # 创建模拟配置
        self.mock_config = Mock(spec=Config)
        self.mock_config.unicom_api_url = "https://test.api.com"
        self.mock_config.max_retries = 3
        self.mock_config.retry_delay = 1
        self.mock_config.get_api_params.return_value = {
            "provinceCode": "17",
            "cityCode": "166",
            "amounts": "100"
        }
        self.mock_config.get_request_headers.return_value = {
            "User-Agent": "test-agent"
        }
        
        self.client = UnicomAPIClient(self.mock_config)
    
    def test_generate_callback_name(self):
        """测试回调函数名生成"""
        callback1 = self.client._generate_callback_name()
        callback2 = self.client._generate_callback_name()
        
        # 验证格式
        assert callback1.startswith("jsonp_")
        assert callback2.startswith("jsonp_")
        
        # 验证唯一性
        assert callback1 != callback2
    
    def test_parse_jsonp_response_success(self):
        """测试JSONP响应解析成功"""
        jsonp_response = 'jsonp_1234567890_12345({"rspCode":"0000","numArray":["18561859849"]})'

        result = self.client._parse_jsonp_response(jsonp_response)

        assert result is not None
        assert result["rspCode"] == "0000"
        assert result["numArray"] == ["18561859849"]
    
    def test_parse_jsonp_response_invalid_format(self):
        """测试无效JSONP格式"""
        invalid_responses = [
            "invalid response",
            "jsonp_123_456(",
            "jsonp_123_456(invalid json)",
            ""
        ]
        
        for response in invalid_responses:
            result = self.client._parse_jsonp_response(response)
            assert result is None
    
    def test_extract_phone_numbers_success(self):
        """测试手机号码提取成功"""
        api_data = {
            "numArray": [
                "18561859849", "0", "0", "0", "6", "0", "0", "1", "6", "0", "6", "6", "1", "6", "1", "0", "99",
                "13061298941", "0", "0", "0", "6", "0", "0", "1", "6", "0", "6", "6", "1", "6", "1", "0", "99"
            ],
            "splitLen": "17"
        }
        
        phone_numbers = self.client._extract_phone_numbers(api_data)
        
        assert len(phone_numbers) == 2
        assert "18561859849" in phone_numbers
        assert "13061298941" in phone_numbers
    
    def test_extract_phone_numbers_no_numarray(self):
        """测试缺少numArray字段"""
        api_data = {"rspCode": "0000"}
        
        phone_numbers = self.client._extract_phone_numbers(api_data)
        
        assert len(phone_numbers) == 0
    
    def test_extract_phone_numbers_invalid_length(self):
        """测试无效长度的号码"""
        api_data = {
            "numArray": [
                "123456789",  # 长度不足11位
                "0", "0", "0", "6", "0", "0", "1", "6", "0", "6", "6", "1", "6", "1", "0", "99"
            ],
            "splitLen": "17"
        }
        
        phone_numbers = self.client._extract_phone_numbers(api_data)
        
        assert len(phone_numbers) == 0
    
    @patch('requests.Session.get')
    def test_fetch_phone_numbers_success(self, mock_get):
        """测试成功获取手机号码"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.text = 'jsonp_1234567890_12345({"rspCode":"0000","numArray":["18561859849","0","0","0","6","0","0","1","6","0","6","6","1","6","1","0","99"],"splitLen":"17"})'
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        phone_numbers = self.client.fetch_phone_numbers()
        
        assert len(phone_numbers) == 1
        assert "18561859849" in phone_numbers
        mock_get.assert_called_once()
    
    @patch('requests.Session.get')
    def test_fetch_phone_numbers_api_error(self, mock_get):
        """测试API返回错误"""
        # 模拟API错误响应
        mock_response = Mock()
        mock_response.text = 'jsonp_1234567890_12345({"rspCode":"9999","rspDesc":"系统错误"})'
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        phone_numbers = self.client.fetch_phone_numbers()
        
        assert len(phone_numbers) == 0
    
    @patch('requests.Session.get')
    def test_fetch_phone_numbers_network_error(self, mock_get):
        """测试网络错误"""
        # 模拟网络错误
        mock_get.side_effect = Exception("Network error")
        
        phone_numbers = self.client.fetch_phone_numbers()
        
        assert len(phone_numbers) == 0
        # 验证重试次数
        assert mock_get.call_count == self.mock_config.max_retries
    
    @patch('requests.Session.get')
    @patch('time.sleep')
    def test_fetch_phone_numbers_retry_mechanism(self, mock_sleep, mock_get):
        """测试重试机制"""
        # 前两次失败，第三次成功
        mock_responses = [
            Exception("First failure"),
            Exception("Second failure"),
            Mock()
        ]
        
        # 设置第三次成功的响应
        success_response = Mock()
        success_response.text = 'jsonp_1234567890_12345({"rspCode":"0000","numArray":["18561859849","0","0","0","6","0","0","1","6","0","6","6","1","6","1","0","99"],"splitLen":"17"})'
        success_response.raise_for_status.return_value = None
        mock_responses[2] = success_response
        
        mock_get.side_effect = mock_responses
        
        phone_numbers = self.client.fetch_phone_numbers()
        
        assert len(phone_numbers) == 1
        assert "18561859849" in phone_numbers
        assert mock_get.call_count == 3
        assert mock_sleep.call_count == 2  # 前两次失败后的sleep
    
    def test_close(self):
        """测试关闭会话"""
        with patch.object(self.client.session, 'close') as mock_close:
            self.client.close()
            mock_close.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
