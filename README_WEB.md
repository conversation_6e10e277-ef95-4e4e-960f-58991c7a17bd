# 联通手机号监控系统 - Web管理界面

一个功能完整的联通手机号码监控系统，包含命令行工具和**现代化Web管理界面**，能够自动检测和通知特殊号码（如豹子号、顺子号等）。

## 🌟 功能特性

### 🌐 Web管理界面（新增）
- **现代化界面**: 响应式设计，支持移动端访问
- **监控控制面板**: 实时显示系统状态，一键启动/停止监控
- **城市配置管理**: 可视化管理城市配置和规则设置
- **数据统计图表**: 交互式图表展示监控数据
- **实时日志查看**: 在线查看系统日志，支持过滤和搜索
- **系统设置**: 在线配置系统参数和通知设置
- **实时更新**: WebSocket支持实时数据推送

### 🏙️ 多城市支持
- **城市配置**: 支持多个城市配置（青岛、济宁等）
- **规则管理**: 每个城市可独立设置靓号规则
- **区号嵌入**: 支持区号嵌入检测（如132-0537-XXXX）
- **归属地查询**: 自动查询手机号归属地信息

### 🔍 智能号码分析
- **豹子号**: 连续相同数字（如1388-8888）
- **顺子号**: 连续递增/递减数字（如1234-5678）
- **AABB模式**: AABB数字模式（如1388-8899）
- **ABAB模式**: ABAB数字模式（如1388-3883）
- **镜像号码**: 回文数字（如138-1234-4321）
- **区号嵌入**: 区号嵌入在号码中

### 🚀 企业级特性
- **Token缓存**: 智能Token管理，支持数据库缓存和自动刷新
- **连接池**: MySQL连接池管理，支持断线重连和健康检查
- **网络重试**: 指数退避重试策略，支持网络状态检测
- **错误恢复**: 完善的异常处理和自动恢复机制
- **生产就绪**: 适合7x24小时稳定运行

## 🚀 快速开始

### 方式一：使用Web管理界面（推荐）

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **配置环境**
```bash
cp config.env.template config.env
# 编辑 config.env 文件配置必要参数
```

3. **启动Web界面**
```bash
python start_web.py
```

4. **访问管理界面**
打开浏览器访问: http://localhost:5000

### 方式二：使用命令行工具

```bash
# 单次检查
python main.py --once

# 持续监控
python main.py --start
```

## 🌐 Web管理界面功能详解

### 1. 监控控制面板 (/)
![监控面板](docs/images/dashboard.png)

**功能特性：**
- **系统状态监控**: 实时显示监控运行状态、Token状态、数据库连接状态
- **统计数据展示**: 总处理号码数、发现特殊号码数、发现率等关键指标
- **最近发现列表**: 显示最近发现的特殊号码，包含城市标签和详细信息
- **一键控制**: 启动/停止监控，实时刷新数据

**操作说明：**
1. 点击"启动监控"按钮开始监控
2. 实时查看监控状态和统计数据
3. 查看最近发现的特殊号码列表

### 2. 城市配置管理 (/cities)
![城市配置](docs/images/cities.png)

**功能特性：**
- **城市列表**: 显示所有已配置城市（青岛、济宁等）
- **规则配置**: 每个城市可独立勾选启用/禁用的靓号规则
- **实时保存**: 配置更改立即保存到数据库
- **添加城市**: 支持添加新的城市配置

**支持的规则：**
- 豹子号 (leopard): 连续相同数字
- 顺子号 (straight): 连续递增/递减数字
- 重复号 (repeat): 重复数字模式
- AABB模式 (aabb): AABB数字模式
- ABAB模式 (abab): ABAB数字模式
- 镜像号码 (mirror): 回文数字
- 区号嵌入 (area_code_embed): 区号嵌入在号码中

**操作说明：**
1. 查看现有城市配置
2. 切换规则开关启用/禁用特定规则
3. 点击"添加城市"按钮添加新城市

### 3. 数据统计分析 (/statistics)
![数据统计](docs/images/statistics.png)

**功能特性：**
- **类型分布图**: 饼图显示特殊号码类型分布
- **城市分布图**: 柱状图显示按城市的号码分布
- **时间趋势图**: 折线图显示最近30天的发现趋势
- **详细统计表**: 按类型和城市的详细统计数据

**操作说明：**
1. 查看各种统计图表
2. 点击"刷新数据"更新统计信息
3. 点击"导出数据"下载统计报告

### 4. 实时日志查看 (/logs)
![日志查看](docs/images/logs.png)

**功能特性：**
- **日志文件选择**: 选择不同的日志文件查看
- **级别过滤**: 按DEBUG、INFO、WARNING、ERROR等级别过滤
- **关键词搜索**: 支持关键词搜索和高亮显示
- **自动刷新**: 可开启自动刷新功能
- **彩色显示**: 不同级别的日志使用不同颜色显示

**操作说明：**
1. 选择要查看的日志文件
2. 设置过滤条件（级别、关键词）
3. 开启自动刷新实时查看日志

### 5. 系统设置 (/settings)
![系统设置](docs/images/settings.png)

**功能特性：**
- **监控配置**: 请求间隔、重试次数等参数设置
- **钉钉通知**: Webhook地址、安全密钥配置和测试
- **数据库配置**: 数据库连接参数设置和测试
- **配置导入导出**: 支持配置文件的导入和导出

**操作说明：**
1. 修改各项配置参数
2. 测试钉钉通知和数据库连接
3. 保存配置或导入/导出配置文件

## 🏗️ 多城市配置示例

### 城市配置文件 (cities.json)
```json
{
  "166": {
    "city_name": "青岛",
    "area_code": "0532",
    "province_code": "17",
    "enabled_rules": ["leopard", "straight", "repeat", "aabb", "abab", "mirror"],
    "disabled_rules": ["area_code_embed"]
  },
  "158": {
    "city_name": "济宁",
    "area_code": "0537",
    "province_code": "17",
    "enabled_rules": ["leopard", "straight", "repeat", "aabb", "abab", "mirror", "area_code_embed"],
    "disabled_rules": []
  }
}
```

### 规则说明
- **青岛 (166)**: 启用基础规则，禁用区号嵌入
- **济宁 (158)**: 启用所有规则，包括区号嵌入（132-0537-XXXX）

## ⚙️ 配置说明

### 基础配置 (config.env)
```env
# 联通API配置
ECS_TOKEN=your_ecs_token_here
PROVINCE_CODE=17
CITY_CODE=166

# 多城市配置
ENABLED_CITIES=166,158
CITIES_CONFIG_FILE=cities.json

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=unicom_monitor
DB_USER=root
DB_PASSWORD=your_password

# 钉钉通知配置
DINGTALK_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=xxx
DINGTALK_SECRET=your_secret_here

# 监控配置
REQUEST_INTERVAL=20
MAX_RETRIES=3
```

## 🛠️ 系统要求

- Python 3.8+
- MySQL 5.7+ (可选，也支持文件存储)
- 现代浏览器 (Chrome, Firefox, Safari, Edge)
- 网络连接

## 📦 依赖包

```txt
requests>=2.31.0
pymysql>=1.1.0
python-dotenv>=1.0.0
flask>=2.3.0
flask-socketio>=5.3.0
plotly>=5.17.0
```

## 🏗️ 项目结构

```
unicom-monitor/
├── src/unicom_monitor/          # 核心模块
│   ├── config.py               # 配置管理
│   ├── api_client.py           # API客户端
│   ├── number_analyzer.py      # 号码分析器
│   ├── data_storage.py         # 数据存储
│   ├── city_manager.py         # 城市管理
│   └── monitor.py              # 主监控器
├── templates/                   # Web界面模板
│   ├── base.html               # 基础模板
│   ├── index.html              # 监控面板
│   ├── cities.html             # 城市配置
│   ├── statistics.html         # 数据统计
│   ├── logs.html               # 日志查看
│   └── settings.html           # 系统设置
├── web_app.py                  # Web应用
├── start_web.py                # Web启动脚本
├── cities.json                 # 城市配置文件
├── config.env                  # 配置文件
└── README.md                   # 说明文档
```

## 🔧 开发指南

### 添加新的号码规则
1. 在 `NumberType` 枚举中添加新类型
2. 在 `NumberAnalyzer` 类中实现检测方法
3. 在Web界面的城市配置中添加新规则选项

### 自定义Web界面
1. 修改 `templates/` 目录下的HTML模板
2. 在 `web_app.py` 中添加新的路由和API
3. 使用Bootstrap和现代CSS框架进行样式定制

## 🐛 故障排除

### 常见问题

1. **Web界面无法访问**
   - 检查端口5000是否被占用
   - 确认防火墙设置
   - 查看启动日志获取错误信息

2. **Token获取失败**
   - 检查ECS_TOKEN是否正确
   - 在Web界面查看Token状态
   - 查看日志获取详细错误信息

3. **数据库连接失败**
   - 在Web界面的设置页面测试连接
   - 检查数据库配置是否正确
   - 确认数据库服务是否运行

## 📈 使用示例

### Web界面操作流程

1. **启动系统**: 运行 `python start_web.py`
2. **访问界面**: 打开 http://localhost:5000
3. **配置城市**: 在城市配置页面添加或修改城市规则
4. **启动监控**: 在监控面板点击"启动监控"按钮
5. **查看数据**: 在统计页面查看监控数据和图表
6. **查看日志**: 在日志页面实时查看系统运行状态

## 📝 更新日志

### v2.0.0 (2025-07-18)
- ✨ 新增Web管理界面
- 🏙️ 支持多城市配置
- 📊 增强的数据统计和图表
- 🔍 更多靓号规则支持
- ⚡ 实时数据更新
- 📋 在线日志查看
- ⚙️ 可视化系统设置

### v1.0.0 (2025-01-XX)
- 🎉 初始版本发布
- 🔍 基础监控功能
- 🔔 钉钉通知支持
- 💾 MySQL数据库存储

## 📞 联系方式

- 项目维护者: alxxxxla
- 问题反馈: 请使用 GitHub Issues

---

⭐ 如果这个项目对您有帮助，请给它一个星标！

🌐 **Web管理界面让监控变得更简单！**
