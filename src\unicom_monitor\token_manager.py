"""
Token管理模块

负责联通API Token的获取、缓存和自动刷新。
"""

import json
import time
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import requests
import pymysql
from urllib.parse import urlencode

from .config import Config
from .network_utils import NetworkRetryMixin
from .db_manager import DatabaseManager


logger = logging.getLogger(__name__)


class TokenManager(NetworkRetryMixin):
    """Token管理器"""

    def __init__(self, config: Config, db_manager: Optional[DatabaseManager] = None) -> None:
        """
        初始化Token管理器

        Args:
            config: 配置对象
            db_manager: 数据库管理器
        """
        super().__init__()
        self.config = config
        self.db_manager = db_manager
        self.current_token: Optional[str] = None
        self.token_expires_at: Optional[datetime] = None

        # 创建token缓存表
        self._create_token_table()

    def _create_token_table(self) -> None:
        """创建token缓存表"""
        if not self.db_manager:
            return

        def _operation(connection):
            with connection.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS token_cache (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        token_type VARCHAR(50) NOT NULL DEFAULT 'unicom_api',
                        token_value TEXT NOT NULL,
                        expires_at TIMESTAMP NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        UNIQUE KEY uk_token_type (token_type)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """)
        try:
            self.db_manager.execute_with_retry(_operation)
            logger.info("Token缓存表创建/检查完成")
        except Exception as e:
            logger.error(f"创建Token缓存表失败: {e}")

    def _load_cached_token(self) -> bool:
        """
        从数据库加载缓存的Token

        Returns:
            是否成功加载有效Token
        """
        if not self.db_manager:
            return False

        def _operation(connection):
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT token_value, expires_at
                    FROM token_cache
                    WHERE token_type = 'unicom_api'
                    AND expires_at > NOW()
                    ORDER BY updated_at DESC
                    LIMIT 1
                """)
                return cursor.fetchone()

        try:
            result = self.db_manager.execute_with_retry(_operation)
            if result:
                self.current_token = result['token_value']
                self.token_expires_at = result['expires_at']
                logger.info(f"成功加载缓存Token，过期时间: {self.token_expires_at}")
                return True
        except Exception as e:
            logger.error(f"加载缓存Token失败: {e}")

        return False

    def _save_token_to_cache(self, token: str, expires_in_hours: int = 2) -> None:
        """
        保存Token到数据库缓存

        Args:
            token: Token值
            expires_in_hours: 过期时间（小时）
        """
        if not self.db_manager:
            return

        def _operation(connection):
            expires_at = datetime.now() + timedelta(hours=expires_in_hours)
            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO token_cache (token_type, token_value, expires_at)
                    VALUES ('unicom_api', %s, %s)
                    ON DUPLICATE KEY UPDATE
                    token_value = VALUES(token_value),
                    expires_at = VALUES(expires_at),
                    updated_at = CURRENT_TIMESTAMP
                """, (token, expires_at))
            return expires_at

        try:
            expires_at = self.db_manager.execute_with_retry(_operation)
            self.current_token = token
            self.token_expires_at = expires_at
            logger.info(f"Token已保存到缓存，过期时间: {expires_at}")
        except Exception as e:
            logger.error(f"保存Token到缓存失败: {e}")

    def _fetch_new_token(self) -> Optional[str]:
        """
        获取新的Token

        Returns:
            新的Token值，失败返回None
        """
        logger.info("开始获取新的联通API Token...")

        # 第一步：获取ticket
        ticket = self._get_ticket()
        if not ticket:
            return None

        # 第二步：使用ticket获取token
        token = self._get_token_with_ticket(ticket)
        if token:
            # 保存到缓存
            self._save_token_to_cache(token)

        return token

    def _get_ticket(self) -> Optional[str]:
        """获取ticket"""
        url = "https://m.client.10010.com/mobileService/openPlatform/openPlatLineNew.htm"
        params = {
            "to_url": "https://card.10010.com/terminal/secondaryCardCom?pull_login=1&salesId=98X2103051447472194&channel=06-0324-aseo-b29o"
        }
        headers = {
            "Host": "m.client.10010.com",
            "Cookie": f"ecs_token={self.config.ecs_token};",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache",
            "Upgrade-Insecure-Requests": "1",
            "User-Agent": "Mozilla/5.0 (Linux; Android 15; PJE110 Build/TP1A.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/130.0.6723.58 Mobile Safari/537.36; unicom{version:android@12.0500,desmobile:0};devicetype{deviceBrand:OnePlus,deviceModel:PJE110};OSVersion/15;ltst;",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "X-Requested-With": "com.sinovatech.unicom.ui",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-User": "?1",
            "Sec-Fetch-Dest": "document",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"
        }

        def _request():
            response = requests.get(url, params=params, headers=headers, allow_redirects=False, timeout=15)
            response.raise_for_status()

            if response.status_code == 302:
                location = response.headers.get("Location")
                if not location:
                    raise ValueError("302重定向响应中未找到Location头")

                # 从Location头中提取ticket
                from urllib.parse import urlparse, parse_qs
                parsed_url = urlparse(location)
                query_params = parse_qs(parsed_url.query)
                ticket = query_params.get("ticket", [None])[0]

                if not ticket:
                    raise ValueError("无法从Location头中解析ticket")

                logger.info(f"成功获取ticket: {ticket[:10]}...")
                return ticket
            else:
                raise ValueError(f"获取ticket失败，状态码: {response.status_code}")

        try:
            return self.retry_with_backoff(_request, "获取ticket")
        except Exception as e:
            logger.error(f"获取ticket失败: {e}")
            return None

    def _get_token_with_ticket(self, ticket: str) -> Optional[str]:
        """使用ticket获取numToken"""
        url = "https://card.10010.com/mall-order/ticket/check/v1"
        params = {
            "ticket": ticket,
            "reqType": "3"
        }

        headers = {
            "Host": "card.10010.com",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache",
            "User-Agent": "Mozilla/5.0 (Linux; Android 15; PJE110 Build/TP1A.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/130.0.6723.58 Mobile Safari/537.36; unicom{version:android@12.0500,desmobile:0};devicetype{deviceBrand:OnePlus,deviceModel:PJE110};OSVersion/15;ltst;",
            "Accept": "application/json, text/plain, */*",
            "X-Requested-With": "com.sinovatech.unicom.ui",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"
        }

        def _request():
            response = requests.get(url, params=params, headers=headers, timeout=15)
            response.raise_for_status()

            data = response.json()
            if data.get("code") == "0000":
                num_token = data.get("numToken")
                if not num_token:
                    raise ValueError("API响应成功，但未找到numToken")
                logger.info("成功获取numToken")
                return num_token
            else:
                error_msg = data.get('msg', '未知错误')
                raise ValueError(f"获取numToken失败: {error_msg}")

        try:
            return self.retry_with_backoff(_request, "获取numToken")
        except Exception as e:
            logger.error(f"使用ticket获取numToken失败: {e}")
            return None

    def get_valid_token(self) -> Optional[str]:
        """
        获取有效的Token

        Returns:
            有效的Token，失败返回None
        """
        # 检查当前Token是否有效
        if self.current_token and self.token_expires_at:
            if datetime.now() < self.token_expires_at - timedelta(minutes=10):  # 提前10分钟刷新
                return self.current_token

        # 尝试从缓存加载
        if self._load_cached_token():
            return self.current_token

        # 获取新Token
        new_token = self._fetch_new_token()
        return new_token

    def is_token_valid(self) -> bool:
        """检查当前Token是否有效"""
        if not self.current_token or not self.token_expires_at:
            return False
        return datetime.now() < self.token_expires_at

    def refresh_token(self) -> bool:
        """
        强制刷新Token

        Returns:
            是否刷新成功
        """
        logger.info("强制刷新Token...")
        new_token = self._fetch_new_token()
        return new_token is not None

