{% extends "base.html" %}

{% block title %}城市配置 - 联通手机号监控系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-city me-2"></i>
        城市配置管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCityModal">
            <i class="fas fa-plus me-1"></i>添加城市
        </button>
    </div>
</div>

<!-- 城市配置卡片 -->
<div class="row">
    {% for city_code, city_config in cities.items() %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card city-rule-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    {{ city_config.city_name }}
                </h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="editCity('{{ city_code }}')">
                            <i class="fas fa-edit me-2"></i>编辑
                        </a></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteCity('{{ city_code }}')">
                            <i class="fas fa-trash me-2"></i>删除
                        </a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">城市代码:</small>
                    <span class="badge bg-secondary ms-1">{{ city_code }}</span>
                </div>
                <div class="mb-3">
                    <small class="text-muted">区号:</small>
                    <span class="badge bg-info ms-1">{{ city_config.area_code }}</span>
                </div>
                
                <h6 class="mb-3">
                    <i class="fas fa-cogs me-2"></i>
                    靓号规则配置
                </h6>
                
                <div class="rules-container" id="rules-{{ city_code }}">
                    {% for rule in available_rules %}
                    <div class="form-check form-switch mb-2">
                        <input class="form-check-input rule-toggle" type="checkbox" 
                               id="rule-{{ city_code }}-{{ rule.id }}"
                               data-city="{{ city_code }}" 
                               data-rule="{{ rule.id }}"
                               {% if rule.id in city_config.enabled_rules %}checked{% endif %}
                               onchange="toggleRule('{{ city_code }}', '{{ rule.id }}', this.checked)">
                        <label class="form-check-label" for="rule-{{ city_code }}-{{ rule.id }}">
                            <strong>{{ rule.name }}</strong>
                            <br>
                            <small class="text-muted">{{ rule.description }}</small>
                        </label>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">
                        启用规则: {{ city_config.enabled_rules|length }}/{{ available_rules|length }}
                    </small>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- 添加城市模态框 -->
<div class="modal fade" id="addCityModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    添加新城市
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCityForm">
                    <div class="mb-3">
                        <label for="cityCode" class="form-label">城市代码 *</label>
                        <input type="text" class="form-control" id="cityCode" required 
                               placeholder="例如: 176">
                        <div class="form-text">联通API中的城市代码</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="cityName" class="form-label">城市名称 *</label>
                        <input type="text" class="form-control" id="cityName" required 
                               placeholder="例如: 济南">
                    </div>
                    
                    <div class="mb-3">
                        <label for="areaCode" class="form-label">区号 *</label>
                        <input type="text" class="form-control" id="areaCode" required 
                               placeholder="例如: 0531">
                    </div>
                    
                    <div class="mb-3">
                        <label for="provinceCode" class="form-label">省份代码</label>
                        <input type="text" class="form-control" id="provinceCode" value="17" 
                               placeholder="例如: 17">
                        <div class="form-text">山东省代码为17</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">启用的规则</label>
                        <div class="border rounded p-3">
                            {% for rule in available_rules %}
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" 
                                       id="newRule{{ rule.id }}" value="{{ rule.id }}"
                                       {% if rule.id != 'area_code_embed' %}checked{% endif %}>
                                <label class="form-check-label" for="newRule{{ rule.id }}">
                                    <strong>{{ rule.name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ rule.description }}</small>
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addCity()">
                    <i class="fas fa-plus me-1"></i>添加城市
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑城市模态框 -->
<div class="modal fade" id="editCityModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    编辑城市配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editCityForm">
                    <input type="hidden" id="editCityCode">
                    
                    <div class="mb-3">
                        <label for="editCityName" class="form-label">城市名称</label>
                        <input type="text" class="form-control" id="editCityName" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editAreaCode" class="form-label">区号</label>
                        <input type="text" class="form-control" id="editAreaCode" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">规则配置</label>
                        <div class="border rounded p-3" id="editRulesContainer">
                            <!-- 动态生成规则选项 -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveCity()">
                    <i class="fas fa-save me-1"></i>保存更改
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 切换规则状态
    async function toggleRule(cityCode, ruleId, enabled) {
        try {
            // 获取当前城市的所有规则状态
            const rulesContainer = document.getElementById(`rules-${cityCode}`);
            const checkboxes = rulesContainer.querySelectorAll('input[type="checkbox"]');
            
            const enabledRules = [];
            const disabledRules = [];
            
            checkboxes.forEach(checkbox => {
                const rule = checkbox.dataset.rule;
                if (checkbox.checked) {
                    enabledRules.push(rule);
                } else {
                    disabledRules.push(rule);
                }
            });
            
            const result = await apiRequest(`/api/cities/${cityCode}`, {
                method: 'PUT',
                body: JSON.stringify({
                    enabled_rules: enabledRules,
                    disabled_rules: disabledRules
                })
            });
            
            if (result.success) {
                showNotification(`${enabled ? '启用' : '禁用'}规则成功`, 'success');
            } else {
                // 恢复原状态
                const checkbox = document.getElementById(`rule-${cityCode}-${ruleId}`);
                checkbox.checked = !enabled;
                showNotification(result.message, 'danger');
            }
        } catch (error) {
            console.error('切换规则失败:', error);
            showNotification('操作失败', 'danger');
        }
    }
    
    // 添加城市
    async function addCity() {
        const form = document.getElementById('addCityForm');
        const formData = new FormData(form);
        
        // 获取启用的规则
        const enabledRules = [];
        const ruleCheckboxes = form.querySelectorAll('input[type="checkbox"]:checked');
        ruleCheckboxes.forEach(checkbox => {
            enabledRules.push(checkbox.value);
        });
        
        const cityData = {
            city_code: document.getElementById('cityCode').value,
            city_name: document.getElementById('cityName').value,
            area_code: document.getElementById('areaCode').value,
            province_code: document.getElementById('provinceCode').value || '17',
            enabled_rules: enabledRules,
            disabled_rules: []
        };
        
        const result = await apiRequest('/api/cities', {
            method: 'POST',
            body: JSON.stringify(cityData)
        });
        
        if (result.success) {
            showNotification('城市添加成功', 'success');
            const modal = bootstrap.Modal.getInstance(document.getElementById('addCityModal'));
            modal.hide();
            
            // 刷新页面
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(result.message, 'danger');
        }
    }
    
    // 编辑城市
    function editCity(cityCode) {
        // 这里可以实现编辑功能
        showNotification('编辑功能开发中...', 'info');
    }
    
    // 删除城市
    function deleteCity(cityCode) {
        if (confirm('确定要删除这个城市配置吗？')) {
            showNotification('删除功能开发中...', 'info');
        }
    }
    
    // 保存城市编辑
    function saveCity() {
        showNotification('保存功能开发中...', 'info');
    }
    
    // 表单验证
    document.getElementById('addCityForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addCity();
    });
</script>
{% endblock %}
