import argparse
import logging
import sys
import time
from pathlib import Path

# 添加src目录到Python路径
# sys.path.insert(0, str(Path(__file__).parent.parent))

from src.unicom_monitor.monitor import PhoneNumberMonitor
from src.unicom_monitor.config import Config
from src.unicom_monitor.api_client import UnicomAPIClient
from src.unicom_monitor.data_storage import DataStorage

# --- 全局设置 ---
# 找到的号码将保存在这个文件中
SEARCH_OUTPUT_FILE = "found_numbers_search.txt"
# 搜索状态文件
SEARCH_STATE_FILE = "search_state.txt"


def setup_logging(config: Config):
    """配置日志记录器"""
    log_level = getattr(logging, config.log_level.upper(), logging.INFO)
    log_file = Path(config.log_file)
    log_file.parent.mkdir(exist_ok=True)

    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    logging.getLogger("urllib3").setLevel(logging.WARNING)


def run_monitor(config_path: str):
    """运行持续监控模式"""
    logging.info("正在创建PhoneNumberMonitor实例...")
    monitor = PhoneNumberMonitor(config_path)
    logging.info("PhoneNumberMonitor实例创建成功。")
    monitor.run()


def run_keyword_search(config: Config, keyword: str):
    """运行单次关键词搜索模式"""
    logging.info(f"开始关键词搜索，关键词: '{keyword}'")
    
    storage = DataStorage(config)
    api_client = UnicomAPIClient(config, storage.db_manager)
    
    if not api_client.unicom_token:
        if not api_client._initialize_token():
            logging.error("无法获取有效的API Token，搜索中止。")
            return

    numbers = api_client.search_numbers_by_keyword(keyword)
    
    if numbers:
        print("\n--- 搜索结果 ---")
        for num in numbers:
            print(f"  - {num}")
        print("----------------")
        
        # 可选择将结果保存到文件
        try:
            with open(SEARCH_OUTPUT_FILE, "a", encoding="utf-8") as f:
                f.write(f"# Keyword: {keyword}\n")
                for num in numbers:
                    f.write(f"{num}\n")
                f.write("\n")
            logging.info(f"搜索结果已追加到 {SEARCH_OUTPUT_FILE}")
        except IOError as e:
            logging.error(f"写入文件 {SEARCH_OUTPUT_FILE} 时出错: {e}")
            
    else:
        logging.info("未找到匹配的号码。")
        
    api_client.close()


def run_spiral4_search(config: Config):
    """运行尾号ABCDABCD回旋搜索模式"""
    logging.info("--- 启动联通靓号搜索程序 (ABCDABCD模式) ---")
    logging.info(f"目标地区: 省份代码={config.province_code}, 城市代码={config.city_code}")
    logging.info(f"结果将保存到: {SEARCH_OUTPUT_FILE}")
    logging.info(f"每次搜索延迟: {config.request_interval} 秒")
    
    storage = DataStorage(config)
    api_client = UnicomAPIClient(config, storage.db_manager)

    if not api_client.unicom_token:
        if not api_client._initialize_token():
            logging.error("无法获取有效的API Token，搜索中止。")
            return

    # 加载已发现的号码
    found_numbers_set = set()
    try:
        with open(SEARCH_OUTPUT_FILE, "r", encoding="utf-8") as f:
            for line in f:
                if not line.startswith("#") and line.strip():
                    found_numbers_set.add(line.strip())
        logging.info(f"从 {SEARCH_OUTPUT_FILE} 加载了 {len(found_numbers_set)} 个已发现的号码。")
    except FileNotFoundError:
        logging.info(f"未找到历史记录文件 {SEARCH_OUTPUT_FILE}，将创建新文件。")

    # 恢复进度
    start_pattern = 0
    try:
        with open(SEARCH_STATE_FILE, "r") as f:
            content = f.read().strip()
            if content.isdigit() and len(content) == 4:
                start_pattern = int(content) + 1
                logging.info(f"检测到上次搜索进度，将从模式 {str(start_pattern).zfill(4)} 继续。")
    except FileNotFoundError:
        logging.info(f"未找到搜索历史，将从 0000 开始。")
    except (ValueError, IndexError):
        logging.warning(f"进度文件 {SEARCH_STATE_FILE} 内容格式不正确，将从 0000 开始。")

    # 主循环
    total_patterns = 10000
    for i in range(start_pattern, total_patterns):
        abcd_pattern = str(i).zfill(4)
        search_keyword = abcd_pattern * 2
        
        logging.info(f"进度 [{i+1}/{total_patterns}] | 正在搜索尾号: {search_keyword}")
        
        numbers_from_api = api_client.search_numbers_by_keyword(search_keyword)
        
        newly_found = []
        if numbers_from_api:
            for num in numbers_from_api:
                if num.endswith(search_keyword) and num not in found_numbers_set:
                    newly_found.append(num)
                    found_numbers_set.add(num)
        
        if newly_found:
            logging.info(f"找到新号码! 目标尾号: {search_keyword}")
            for number in newly_found:
                print(f"  [+] {number}")
            
            try:
                with open(SEARCH_OUTPUT_FILE, "a", encoding="utf-8") as f:
                    f.write(f"# Pattern: {search_keyword}\n")
                    for number in newly_found:
                        f.write(f"{number}\n")
            except IOError as e:
                logging.error(f"写入文件 {SEARCH_OUTPUT_FILE} 时出错: {e}")

        try:
            with open(SEARCH_STATE_FILE, "w") as f:
                f.write(abcd_pattern)
        except IOError as e:
            logging.error(f"保存搜索进度到 {SEARCH_STATE_FILE} 时出错: {e}")

        time.sleep(config.request_interval)
            
    logging.info("--- ABCDABCD模式搜索完成 ---")
    api_client.close()


def main():
    """主函数，解析参数并启动对应模式"""
    parser = argparse.ArgumentParser(
        description="联通手机号监控和搜索工具",
        formatter_class=argparse.RawTextHelpFormatter
    )
    
    # 通用配置
    parser.add_argument(
        '--config',
        type=str,
        default='config.env',
        help='指定配置文件路径 (默认: config.env)'
    )
    
    # 模式选择
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        '--monitor',
        action='store_true',
        help='启动持续监控模式 (默认行为)'
    )
    mode_group.add_argument(
        '--search',
        type=str,
        metavar='KEYWORD',
        help='按关键词搜索号码 (例如: --search 8888)'
    )
    mode_group.add_argument(
        '--search-spiral4',
        action='store_true',
        help='启动ABCDABCD模式的全量搜索'
    )

    args = parser.parse_args()
    
    # 加载配置并设置日志
    try:
        config = Config(args.config)
        setup_logging(config)
    except FileNotFoundError:
        logging.error(f"错误：配置文件 '{args.config}' 未找到。")
        sys.exit(1)
    
    logging.info("程序启动，开始解析参数...")

    # 根据参数选择执行模式
    if args.search:
        run_keyword_search(config, args.search)
    elif args.search_spiral4:
        run_spiral4_search(config)
    else:
        # 默认或明确指定 --monitor
        run_monitor(args.config)


if __name__ == "__main__":
    main() 