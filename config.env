# 联通API配置
UNICOM_API_URL=https://card.10010.com/NumDockerApp/NumberCenter/qryNum/newRecToken
# 重要：从联通APP抓包获取的ecs_token，用于自动刷新接口TOKEN
ECS_TOKEN=eyJkYXRhIjoiNjM3ZjRjMzUzMDMzZGE3NzUzMTZiODlkOTc0MzBjZjNjOTljZTNmMzM1NjY3N2JlYTg1YTE0MmU0MzY2YmQzNDBmMzc1NDM3NmRjNDUxMjI3OTk4ODJmZTcxNjk2YzU5NmI0M2UwYmVjNjlmYmJkMzYzYmE5M2VhN2MxNjc5YzRiYTExNzUyY2UyNzQzNzgyMTc4OGVkNDBlYmJhODJmZDRiMTUzY2UzYzFjYjkyNjA2NWZhODBhMDFjYWM1YTVhYmQ2YjI3MjlmN2NiNzRhYzg4YmQ2Mzg5ZGJjMTVmZTNlMzgzOGUyNDkzYmJhYTJjODVlOWU0MmU4ODQ3MmM2YTQ2ZDZlNGQwNWE3MjdjMDAwODA4M2I1YjM1ODU0NTAwYzU3YjJiYmJiMDQwNDZjNDkwM2E5NTJiMDk2NWUwM2UzMWY2ZmQ1MDMzMzg1MjYyY2E0YjhjODUxZjlmOTgyYTMwZjM2MDY5ZDY0ZDIwYWM0ZmY5YjRiMTVlNWU5MDIzIiwidmVyc2lvbiI6IjAwIn0=
PROVINCE_CODE=17
CITY_CODE=166
NUM_CLASS=1
AMOUNTS=100
CHANNEL=B2C
CHNL_TYPE=1
GROUP_TYPE=1
CALL_TYPE=1
MONITOR_PURPOSE=1009
SEARCH_RATIO=100
SEARCH_CODE=02
SEARCH_TYPE=1
# SELECT_RULE=101,201,202,301,302
SELECT_RULE=306,307,308,310,311,315,316,317,318,322

# 数据库配置 (MySQL)
DB_HOST=*************
DB_PORT=3306
DB_USER=root
DB_PASSWORD=aA7758521!a
DB_NAME=10010

# 钉钉机器人配置
DINGTALK_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=2ba5229956f0aed9b947a6a1211ecd9699189f253de1cc4fbba9479d1388096b
DINGTALK_SECRET=SECe7a62858a160b75b32de017e61d23216835859941f1e1c07e109527d24592378

# 监控配置
REQUEST_INTERVAL=20  # 请求间隔（秒）
MAX_RETRIES=3      # 最大重试次数
RETRY_DELAY=2      # 重试延迟（秒）

# 数据存储配置 - 已迁移到MySQL
# DATA_DIR=data
# HISTORY_FILE=phone_numbers_history.json
# RESULTS_FILE=special_numbers.csv

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/monitor.log
