{% extends "base.html" %}

{% block title %}数据统计 - 联通手机号监控系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-bar me-2"></i>
        数据统计分析
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary" onclick="refreshCharts()">
                <i class="fas fa-sync-alt me-1"></i>刷新数据
            </button>
            <button type="button" class="btn btn-outline-success" onclick="exportData()">
                <i class="fas fa-download me-1"></i>导出数据
            </button>
        </div>
    </div>
</div>

<!-- 总体统计卡片 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h4">{{ stats.total_processed or 0 }}</div>
                        <div>总处理号码</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-mobile-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h4">{{ stats.total_special or 0 }}</div>
                        <div>发现特殊号码</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-star fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h4">
                            {% if stats.total_processed and stats.total_processed > 0 %}
                                {{ "%.2f"|format((stats.total_special or 0) / stats.total_processed * 100) }}%
                            {% else %}
                                0.00%
                            {% endif %}
                        </div>
                        <div>发现率</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h4">{{ stats.type_distribution|length or 0 }}</div>
                        <div>号码类型</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tags fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 特殊号码类型分布 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-pie-chart me-2"></i>
                    特殊号码类型分布
                </h5>
            </div>
            <div class="card-body">
                {% if charts.type_distribution %}
                    <div id="typeDistributionChart"></div>
                {% else %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-chart-pie fa-3x mb-3"></i>
                        <p>暂无数据</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 城市分布 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    按城市分布
                </h5>
            </div>
            <div class="card-body">
                {% if charts.city_distribution %}
                    <div id="cityDistributionChart"></div>
                {% else %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-chart-bar fa-3x mb-3"></i>
                        <p>暂无数据</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 时间趋势图 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    发现趋势（最近30天）
                </h5>
            </div>
            <div class="card-body">
                {% if charts.time_trend %}
                    <div id="timeTrendChart"></div>
                {% else %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-chart-line fa-3x mb-3"></i>
                        <p>暂无数据</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 详细统计表格 -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    类型统计详情
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>号码类型</th>
                                <th>数量</th>
                                <th>占比</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for type_name, count in stats.type_distribution.items() %}
                            <tr>
                                <td>{{ type_name }}</td>
                                <td><span class="badge bg-primary">{{ count }}</span></td>
                                <td>
                                    {% if stats.total_special and stats.total_special > 0 %}
                                        {{ "%.1f"|format(count / stats.total_special * 100) }}%
                                    {% else %}
                                        0.0%
                                    {% endif %}
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center text-muted">暂无数据</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-city me-2"></i>
                    城市统计详情
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>城市</th>
                                <th>数量</th>
                                <th>占比</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for city_name, count in city_stats.items() %}
                            <tr>
                                <td>{{ city_name }}</td>
                                <td><span class="badge bg-success">{{ count }}</span></td>
                                <td>
                                    {% if stats.total_special and stats.total_special > 0 %}
                                        {{ "%.1f"|format(count / stats.total_special * 100) }}%
                                    {% else %}
                                        0.0%
                                    {% endif %}
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center text-muted">暂无数据</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 渲染图表
    function renderCharts() {
        {% if charts.type_distribution %}
        // 类型分布饼图
        const typeData = {{ charts.type_distribution|safe }};
        Plotly.newPlot('typeDistributionChart', typeData.data, typeData.layout, {responsive: true});
        {% endif %}
        
        {% if charts.city_distribution %}
        // 城市分布柱状图
        const cityData = {{ charts.city_distribution|safe }};
        Plotly.newPlot('cityDistributionChart', cityData.data, cityData.layout, {responsive: true});
        {% endif %}
        
        {% if charts.time_trend %}
        // 时间趋势图
        const timeData = {{ charts.time_trend|safe }};
        Plotly.newPlot('timeTrendChart', timeData.data, timeData.layout, {responsive: true});
        {% endif %}
    }
    
    // 刷新图表
    async function refreshCharts() {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>刷新中...';
        btn.disabled = true;
        
        try {
            const result = await apiRequest('/api/statistics');
            if (result.success) {
                showNotification('数据已刷新', 'success');
                // 重新加载页面以更新图表
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification('刷新失败: ' + result.message, 'danger');
            }
        } catch (error) {
            showNotification('刷新失败', 'danger');
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }
    
    // 导出数据
    async function exportData() {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>导出中...';
        btn.disabled = true;
        
        try {
            // 这里可以实现数据导出功能
            showNotification('导出功能开发中...', 'info');
        } catch (error) {
            showNotification('导出失败', 'danger');
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }
    
    // 页面加载完成后渲染图表
    document.addEventListener('DOMContentLoaded', function() {
        renderCharts();
        
        // 监听窗口大小变化，重新调整图表
        window.addEventListener('resize', function() {
            setTimeout(renderCharts, 100);
        });
    });
</script>
{% endblock %}
