#!/usr/bin/env python3
"""
启动Web管理界面的脚本
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

if __name__ == "__main__":
    print("🌐 启动联通手机号监控系统 Web 管理界面")
    print("=" * 60)
    
    try:
        from web_app import app, socketio, init_app
        
        if init_app():
            print("✅ Web应用初始化成功")
            print("🚀 启动Web服务器...")
            print("📱 访问地址: http://localhost:5000")
            print("⚡ 支持实时数据更新")
            print("🛠️ 功能完整的管理界面")
            print("=" * 60)
            print("💡 提示: 按 Ctrl+C 停止服务器")
            print()
            
            # 启动Web服务器
            socketio.run(app, host='0.0.0.0', port=5000, debug=False)
        else:
            print("❌ Web应用初始化失败，请检查配置")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n👋 Web服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
