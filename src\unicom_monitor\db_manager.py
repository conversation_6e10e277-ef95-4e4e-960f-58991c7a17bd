"""
数据库连接管理模块

提供MySQL连接池、断线重连、健康检查等功能。
"""

import time
import logging
import threading
from typing import Optional, Dict, Any
from contextlib import contextmanager
import pymysql
from pymysql.err import OperationalError, InterfaceError
from queue import Queue, Empty

from .config import Config


logger = logging.getLogger(__name__)


class DatabaseConnectionPool:
    """数据库连接池"""
    
    def __init__(self, config: Config, pool_size: int = 5, max_overflow: int = 10):
        """
        初始化连接池
        
        Args:
            config: 配置对象
            pool_size: 连接池大小
            max_overflow: 最大溢出连接数
        """
        self.config = config
        self.pool_size = pool_size
        self.max_overflow = max_overflow
        self.pool = Queue(maxsize=pool_size + max_overflow)
        self.current_connections = 0
        self.lock = threading.Lock()
        self.last_health_check = 0
        self.health_check_interval = 300  # 5分钟检查一次
        
        # 初始化连接池
        self._initialize_pool()
    
    def _create_connection(self) -> Optional[pymysql.Connection]:
        """创建新的数据库连接"""
        try:
            connection = pymysql.connect(
                host=self.config.db_host,
                port=self.config.db_port,
                user=self.config.db_user,
                password=self.config.db_password,
                database=self.config.db_name,
                charset='utf8mb4',
                autocommit=True,
                connect_timeout=10,
                read_timeout=30,
                write_timeout=30
            )
            logger.debug("创建新的数据库连接")
            return connection
        except Exception as e:
            logger.error(f"创建数据库连接失败: {e}")
            return None
    
    def _initialize_pool(self) -> None:
        """初始化连接池"""
        logger.info(f"初始化数据库连接池，大小: {self.pool_size}")
        
        for _ in range(self.pool_size):
            connection = self._create_connection()
            if connection:
                self.pool.put(connection)
                self.current_connections += 1
            else:
                logger.warning("初始化连接池时创建连接失败")
                break
        
        logger.info(f"连接池初始化完成，当前连接数: {self.current_connections}")
    
    def _is_connection_alive(self, connection: pymysql.Connection) -> bool:
        """检查连接是否存活"""
        try:
            connection.ping(reconnect=False)
            return True
        except Exception:
            return False
    
    def _health_check(self) -> None:
        """连接池健康检查"""
        current_time = time.time()
        if current_time - self.last_health_check < self.health_check_interval:
            return
        
        self.last_health_check = current_time
        logger.debug("执行连接池健康检查")
        
        # 检查池中的连接
        healthy_connections = []
        dead_connections = 0
        
        # 临时取出所有连接进行检查
        temp_connections = []
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                temp_connections.append(conn)
            except Empty:
                break
        
        # 检查每个连接的健康状态
        for conn in temp_connections:
            if self._is_connection_alive(conn):
                healthy_connections.append(conn)
            else:
                dead_connections += 1
                try:
                    conn.close()
                except:
                    pass
        
        # 将健康的连接放回池中
        for conn in healthy_connections:
            self.pool.put(conn)
        
        # 更新连接计数
        with self.lock:
            self.current_connections = len(healthy_connections)
        
        if dead_connections > 0:
            logger.warning(f"健康检查发现 {dead_connections} 个死连接已清理")
            
            # 补充新连接
            for _ in range(min(dead_connections, self.pool_size - self.current_connections)):
                new_conn = self._create_connection()
                if new_conn:
                    self.pool.put(new_conn)
                    with self.lock:
                        self.current_connections += 1
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接（上下文管理器）
        
        Yields:
            数据库连接对象
        """
        connection = None
        try:
            # 执行健康检查
            self._health_check()
            
            # 尝试从池中获取连接
            try:
                connection = self.pool.get(timeout=5)
            except Empty:
                # 池中没有可用连接，尝试创建新连接
                with self.lock:
                    if self.current_connections < self.pool_size + self.max_overflow:
                        connection = self._create_connection()
                        if connection:
                            self.current_connections += 1
                        else:
                            raise Exception("无法创建新的数据库连接")
                    else:
                        raise Exception("连接池已满，无法获取连接")
            
            # 检查连接是否存活
            if not self._is_connection_alive(connection):
                logger.warning("获取到的连接已失效，尝试重新连接")
                try:
                    connection.close()
                except:
                    pass
                
                connection = self._create_connection()
                if not connection:
                    raise Exception("重新创建连接失败")
            
            yield connection
            
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            raise
        finally:
            # 将连接放回池中
            if connection:
                try:
                    if self._is_connection_alive(connection):
                        self.pool.put(connection)
                    else:
                        connection.close()
                        with self.lock:
                            self.current_connections -= 1
                except Exception as e:
                    logger.error(f"归还连接时发生错误: {e}")
    
    def close_all(self) -> None:
        """关闭所有连接"""
        logger.info("关闭所有数据库连接")
        
        while not self.pool.empty():
            try:
                connection = self.pool.get_nowait()
                connection.close()
            except Empty:
                break
            except Exception as e:
                logger.error(f"关闭连接时发生错误: {e}")
        
        with self.lock:
            self.current_connections = 0
    
    def get_pool_status(self) -> Dict[str, Any]:
        """获取连接池状态"""
        return {
            "pool_size": self.pool_size,
            "max_overflow": self.max_overflow,
            "current_connections": self.current_connections,
            "available_connections": self.pool.qsize(),
            "last_health_check": self.last_health_check
        }


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: Config):
        """
        初始化数据库管理器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.pool: Optional[DatabaseConnectionPool] = None
        self.is_connected = False
        
        # 尝试初始化连接池
        self._initialize()
    
    def _initialize(self) -> None:
        """初始化数据库连接"""
        try:
            self.pool = DatabaseConnectionPool(self.config)
            
            # 测试连接
            with self.pool.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
            
            self.is_connected = True
            logger.info("数据库连接池初始化成功")
            
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            self.is_connected = False
            self.pool = None
    
    def get_connection(self):
        """获取数据库连接"""
        if not self.is_connected or not self.pool:
            # 尝试重新连接
            self._initialize()
            
        if not self.pool:
            raise Exception("数据库连接不可用")
            
        return self.pool.get_connection()
    
    def execute_with_retry(self, operation, *args, **kwargs):
        """
        带重试的数据库操作
        
        Args:
            operation: 要执行的操作函数
            *args: 操作函数的位置参数
            **kwargs: 操作函数的关键字参数
            
        Returns:
            操作结果
        """
        max_retries = 3
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                with self.get_connection() as conn:
                    return operation(conn, *args, **kwargs)
                    
            except (OperationalError, InterfaceError) as e:
                last_exception = e
                logger.warning(f"数据库操作失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                
                # 重置连接状态
                self.is_connected = False
                
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    
            except Exception as e:
                logger.error(f"数据库操作发生未知错误: {e}")
                raise
        
        # 所有重试都失败
        if last_exception:
            raise last_exception
        else:
            raise Exception("数据库操作失败")
    
    def is_healthy(self) -> bool:
        """检查数据库连接是否健康"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
            return True
        except Exception:
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取数据库状态"""
        status = {
            "is_connected": self.is_connected,
            "is_healthy": self.is_healthy()
        }
        
        if self.pool:
            status.update(self.pool.get_pool_status())
            
        return status
    
    def close(self) -> None:
        """关闭数据库管理器"""
        if self.pool:
            self.pool.close_all()
        self.is_connected = False
