{% extends "base.html" %}

{% block title %}系统设置 - 联通手机号监控系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cog me-2"></i>
        系统设置
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-success" onclick="saveSettings()">
                <i class="fas fa-save me-1"></i>保存设置
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="resetSettings()">
                <i class="fas fa-undo me-1"></i>重置
            </button>
        </div>
    </div>
</div>

<div class="row">
    <!-- 监控配置 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    监控配置
                </h5>
            </div>
            <div class="card-body">
                <form id="monitorSettings">
                    <div class="mb-3">
                        <label for="requestInterval" class="form-label">请求间隔（秒）</label>
                        <input type="number" class="form-control" id="requestInterval" 
                               value="{{ config.request_interval }}" min="10" max="300">
                        <div class="form-text">建议设置为20-60秒，避免请求过于频繁</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="maxRetries" class="form-label">最大重试次数</label>
                        <input type="number" class="form-control" id="maxRetries" 
                               value="3" min="1" max="10">
                        <div class="form-text">网络请求失败时的重试次数</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="retryDelay" class="form-label">重试延迟（秒）</label>
                        <input type="number" class="form-control" id="retryDelay" 
                               value="2" min="1" max="30">
                        <div class="form-text">重试前的等待时间</div>
                    </div>
                    
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="enableDebugMode">
                        <label class="form-check-label" for="enableDebugMode">
                            启用调试模式
                        </label>
                        <div class="form-text">启用后会输出更详细的日志信息</div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 钉钉通知配置 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bell me-2"></i>
                    钉钉通知配置
                </h5>
            </div>
            <div class="card-body">
                <form id="dingtalkSettings">
                    <div class="mb-3">
                        <label for="dingtalkWebhook" class="form-label">Webhook地址</label>
                        <input type="url" class="form-control" id="dingtalkWebhook" 
                               value="{{ config.dingtalk_webhook }}"
                               placeholder="https://oapi.dingtalk.com/robot/send?access_token=...">
                        <div class="form-text">钉钉机器人的Webhook地址</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="dingtalkSecret" class="form-label">安全密钥</label>
                        <input type="password" class="form-control" id="dingtalkSecret" 
                               value="{{ config.dingtalk_secret }}"
                               placeholder="钉钉机器人的安全密钥">
                        <div class="form-text">用于签名验证的密钥</div>
                    </div>
                    
                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-primary" onclick="testDingtalk()">
                            <i class="fas fa-paper-plane me-1"></i>测试通知
                        </button>
                    </div>
                    
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="enableDingtalkNotification" checked>
                        <label class="form-check-label" for="enableDingtalkNotification">
                            启用钉钉通知
                        </label>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 数据库配置 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-database me-2"></i>
                    数据库配置
                </h5>
            </div>
            <div class="card-body">
                <form id="databaseSettings">
                    <div class="mb-3">
                        <label for="dbHost" class="form-label">数据库主机</label>
                        <input type="text" class="form-control" id="dbHost" 
                               value="{{ config.db_host }}"
                               placeholder="localhost">
                    </div>
                    
                    <div class="mb-3">
                        <label for="dbPort" class="form-label">端口</label>
                        <input type="number" class="form-control" id="dbPort" 
                               value="{{ config.db_port }}" min="1" max="65535">
                    </div>
                    
                    <div class="mb-3">
                        <label for="dbName" class="form-label">数据库名</label>
                        <input type="text" class="form-control" id="dbName" 
                               value="{{ config.db_name }}"
                               placeholder="unicom_monitor">
                    </div>
                    
                    <div class="mb-3">
                        <label for="dbUser" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="dbUser" 
                               value="{{ config.db_user }}"
                               placeholder="root">
                    </div>
                    
                    <div class="mb-3">
                        <label for="dbPassword" class="form-label">密码</label>
                        <input type="password" class="form-control" id="dbPassword" 
                               placeholder="数据库密码">
                        <div class="form-text">出于安全考虑，密码不会显示</div>
                    </div>
                    
                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-primary" onclick="testDatabase()">
                            <i class="fas fa-plug me-1"></i>测试连接
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 系统信息 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    系统信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>系统版本:</strong>
                    </div>
                    <div class="col-sm-8">
                        <span class="badge bg-primary">v2.0.0</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>Python版本:</strong>
                    </div>
                    <div class="col-sm-8">
                        <span id="pythonVersion">-</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>运行时间:</strong>
                    </div>
                    <div class="col-sm-8">
                        <span id="uptime">-</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>配置文件:</strong>
                    </div>
                    <div class="col-sm-8">
                        <code>config.env</code>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>日志目录:</strong>
                    </div>
                    <div class="col-sm-8">
                        <code>logs/</code>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-info" onclick="exportConfig()">
                        <i class="fas fa-download me-1"></i>导出配置
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="importConfig()">
                        <i class="fas fa-upload me-1"></i>导入配置
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="clearLogs()">
                        <i class="fas fa-trash me-1"></i>清理日志
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导入配置模态框 -->
<div class="modal fade" id="importConfigModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>
                    导入配置文件
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="configFile" class="form-label">选择配置文件</label>
                    <input type="file" class="form-control" id="configFile" accept=".json,.env">
                    <div class="form-text">支持JSON格式或ENV格式的配置文件</div>
                </div>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>注意:</strong> 导入配置将覆盖当前设置，请确保备份现有配置。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="doImportConfig()">
                    <i class="fas fa-upload me-1"></i>导入
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 保存设置
    async function saveSettings() {
        const settings = {
            request_interval: parseInt(document.getElementById('requestInterval').value),
            max_retries: parseInt(document.getElementById('maxRetries').value),
            retry_delay: parseInt(document.getElementById('retryDelay').value),
            debug_mode: document.getElementById('enableDebugMode').checked,
            dingtalk_webhook: document.getElementById('dingtalkWebhook').value,
            dingtalk_secret: document.getElementById('dingtalkSecret').value,
            dingtalk_enabled: document.getElementById('enableDingtalkNotification').checked,
            db_host: document.getElementById('dbHost').value,
            db_port: parseInt(document.getElementById('dbPort').value),
            db_name: document.getElementById('dbName').value,
            db_user: document.getElementById('dbUser').value,
            db_password: document.getElementById('dbPassword').value
        };
        
        const result = await apiRequest('/api/settings', {
            method: 'POST',
            body: JSON.stringify(settings)
        });
        
        if (result.success) {
            showNotification('设置保存成功', 'success');
        } else {
            showNotification('设置保存失败: ' + result.message, 'danger');
        }
    }
    
    // 重置设置
    function resetSettings() {
        if (confirm('确定要重置所有设置吗？')) {
            location.reload();
        }
    }
    
    // 测试钉钉通知
    async function testDingtalk() {
        const webhook = document.getElementById('dingtalkWebhook').value;
        const secret = document.getElementById('dingtalkSecret').value;
        
        if (!webhook) {
            showNotification('请先填写Webhook地址', 'warning');
            return;
        }
        
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>发送中...';
        btn.disabled = true;
        
        try {
            // 这里应该调用测试钉钉通知的API
            showNotification('钉钉通知测试功能开发中...', 'info');
        } catch (error) {
            showNotification('测试失败', 'danger');
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }
    
    // 测试数据库连接
    async function testDatabase() {
        const host = document.getElementById('dbHost').value;
        const port = document.getElementById('dbPort').value;
        const dbname = document.getElementById('dbName').value;
        const user = document.getElementById('dbUser').value;
        const password = document.getElementById('dbPassword').value;
        
        if (!host || !dbname || !user) {
            showNotification('请填写完整的数据库信息', 'warning');
            return;
        }
        
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>连接中...';
        btn.disabled = true;
        
        try {
            // 这里应该调用测试数据库连接的API
            showNotification('数据库连接测试功能开发中...', 'info');
        } catch (error) {
            showNotification('连接测试失败', 'danger');
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }
    
    // 导出配置
    function exportConfig() {
        const config = {
            monitor: {
                request_interval: parseInt(document.getElementById('requestInterval').value),
                max_retries: parseInt(document.getElementById('maxRetries').value),
                retry_delay: parseInt(document.getElementById('retryDelay').value),
                debug_mode: document.getElementById('enableDebugMode').checked
            },
            dingtalk: {
                webhook: document.getElementById('dingtalkWebhook').value,
                secret: document.getElementById('dingtalkSecret').value,
                enabled: document.getElementById('enableDingtalkNotification').checked
            },
            database: {
                host: document.getElementById('dbHost').value,
                port: parseInt(document.getElementById('dbPort').value),
                name: document.getElementById('dbName').value,
                user: document.getElementById('dbUser').value
            }
        };
        
        const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `unicom_monitor_config_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
        
        showNotification('配置已导出', 'success');
    }
    
    // 导入配置
    function importConfig() {
        const modal = new bootstrap.Modal(document.getElementById('importConfigModal'));
        modal.show();
    }
    
    // 执行导入配置
    function doImportConfig() {
        const fileInput = document.getElementById('configFile');
        const file = fileInput.files[0];
        
        if (!file) {
            showNotification('请选择配置文件', 'warning');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const config = JSON.parse(e.target.result);
                
                // 应用配置
                if (config.monitor) {
                    document.getElementById('requestInterval').value = config.monitor.request_interval || 20;
                    document.getElementById('maxRetries').value = config.monitor.max_retries || 3;
                    document.getElementById('retryDelay').value = config.monitor.retry_delay || 2;
                    document.getElementById('enableDebugMode').checked = config.monitor.debug_mode || false;
                }
                
                if (config.dingtalk) {
                    document.getElementById('dingtalkWebhook').value = config.dingtalk.webhook || '';
                    document.getElementById('dingtalkSecret').value = config.dingtalk.secret || '';
                    document.getElementById('enableDingtalkNotification').checked = config.dingtalk.enabled !== false;
                }
                
                if (config.database) {
                    document.getElementById('dbHost').value = config.database.host || '';
                    document.getElementById('dbPort').value = config.database.port || 3306;
                    document.getElementById('dbName').value = config.database.name || '';
                    document.getElementById('dbUser').value = config.database.user || '';
                }
                
                const modal = bootstrap.Modal.getInstance(document.getElementById('importConfigModal'));
                modal.hide();
                
                showNotification('配置导入成功', 'success');
            } catch (error) {
                showNotification('配置文件格式错误', 'danger');
            }
        };
        
        reader.readAsText(file);
    }
    
    // 清理日志
    function clearLogs() {
        if (confirm('确定要清理所有日志文件吗？此操作不可恢复。')) {
            showNotification('清理日志功能开发中...', 'info');
        }
    }
    
    // 更新系统信息
    function updateSystemInfo() {
        // 更新Python版本
        document.getElementById('pythonVersion').textContent = 'Python 3.12';
        
        // 更新运行时间
        document.getElementById('uptime').textContent = '运行中...';
    }
    
    // 页面加载时更新系统信息
    document.addEventListener('DOMContentLoaded', function() {
        updateSystemInfo();
    });
</script>
{% endblock %}
