"""
数据存储模块

负责管理手机号码数据的存储，包括历史记录、去重和结果导出。
"""

import json
import csv
import os
import logging
from datetime import datetime
from typing import List, Dict, Set, Any, Optional
import pandas as pd

from .config import Config
from .number_analyzer import SpecialNumber, NumberType
from .db_manager import DatabaseManager


logger = logging.getLogger(__name__)


class DataStorage:
    """数据存储管理器"""

    def __init__(self, config: Config):
        """
        初始化DataStorage

        Args:
            config: 配置对象
        """
        self.config = config
        self.db_manager: Optional[DatabaseManager] = None
        self.use_mysql = False

        # 尝试连接MySQL
        if config.db_host and config.db_user and config.db_name:
            try:
                self.db_manager = DatabaseManager(config)
                if self.db_manager.is_connected:
                    self.use_mysql = True
                    self._create_tables_if_not_exists()
                    logger.info("✅ 成功连接到MySQL数据库")
                    print("✅ 成功连接到MySQL数据库")
            except Exception as e:
                logger.error(f"MySQL连接失败: {e}")
                print(f"❌ MySQL连接失败: {e}")

        # 如果MySQL连接失败，使用文件存储作为fallback
        if not self.use_mysql:
            print("⚠️ MySQL连接失败，使用文件存储作为备用方案")
            logger.warning("MySQL连接失败，使用文件存储作为备用方案")
            self._init_file_storage()



    def _init_file_storage(self) -> None:
        """初始化文件存储"""
        # 创建数据目录
        self.data_dir = "data"
        os.makedirs(self.data_dir, exist_ok=True)

        # 设置文件路径
        self.history_file_path = os.path.join(self.data_dir, "processed_numbers.json")
        self.results_file_path = os.path.join(self.data_dir, "special_numbers.csv")

        # 加载历史记录
        self.processed_numbers: Set[str] = self._load_history_from_file()
        self.special_numbers_history: List[Dict[str, Any]] = self._load_special_numbers_from_file()

    def _load_history_from_file(self) -> Set[str]:
        """从文件加载历史记录"""
        if not os.path.exists(self.history_file_path):
            return set()

        try:
            with open(self.history_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return set(data.get('processed_numbers', []))
        except Exception as e:
            logger.error(f"加载历史记录失败: {e}")
            return set()

    def _load_special_numbers_from_file(self) -> List[Dict[str, Any]]:
        """从文件加载特殊号码记录"""
        if not os.path.exists(self.results_file_path):
            return []

        try:
            df = pd.read_csv(self.results_file_path, encoding='utf-8', dtype=str)
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"加载特殊号码记录失败: {e}")
            return []

    def _create_tables_if_not_exists(self) -> None:
        """创建数据表（如果不存在）"""
        if not self.db_manager:
            logger.error("无法创建表，数据库管理器未初始化")
            return

        def create_tables(connection):
            with connection.cursor() as cursor:
                # 创建处理记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS processed_numbers (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        number VARCHAR(20) NOT NULL UNIQUE,
                        processed_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_number (number)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """)

                # 创建特殊号码表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS special_numbers (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        number VARCHAR(20) NOT NULL,
                        type VARCHAR(50) NOT NULL,
                        pattern VARCHAR(20) NOT NULL,
                        description TEXT,
                        city_code VARCHAR(10),
                        city_name VARCHAR(50),
                        area_code VARCHAR(10),
                        phone_attribution JSON,
                        discovered_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_number (number),
                        INDEX idx_type (type),
                        INDEX idx_city_code (city_code),
                        INDEX idx_discovered_time (discovered_time)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """)

        try:
            self.db_manager.execute_with_retry(create_tables)
            logger.info("数据表创建/检查完成")
        except Exception as e:
            logger.error(f"创建数据表失败: {e}")
            logger.error(f"连接MySQL失败: {e}", exc_info=True)
            # 主动引发异常，让上层调用者（main.py）能够捕获到
            raise e



    def is_number_processed(self, number: str) -> bool:
        """检查号码是否已被处理"""
        if self.use_mysql and self.db_manager:
            def check_processed(connection):
                with connection.cursor() as cursor:
                    cursor.execute("SELECT number FROM processed_numbers WHERE number = %s", (number,))
                    result = cursor.fetchone()
                    return result is not None

            try:
                return self.db_manager.execute_with_retry(check_processed)
            except Exception as e:
                logger.error(f"查询已处理号码失败: {e}")
                return False
        else:
            # 文件存储模式
            return number in self.processed_numbers

    def mark_numbers_processed(self, numbers: List[str]) -> None:
        """将号码列表标记为已处理"""
        if not numbers:
            return

        if self.use_mysql and self.db_manager:
            def mark_processed(connection):
                with connection.cursor() as cursor:
                    query = "INSERT IGNORE INTO processed_numbers (number) VALUES (%s)"
                    data = [(number,) for number in numbers]
                    cursor.executemany(query, data)

            try:
                self.db_manager.execute_with_retry(mark_processed)
                logger.info(f"成功标记 {len(numbers)} 个号码为已处理")
            except Exception as e:
                logger.error(f"标记已处理号码失败: {e}")
        else:
            # 文件存储模式
            self.processed_numbers.update(numbers)
            self._save_history_to_file()

    def _save_history_to_file(self) -> None:
        """保存历史记录到文件"""
        try:
            data = {
                'processed_numbers': list(self.processed_numbers),
                'last_updated': datetime.now().isoformat()
            }
            with open(self.history_file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存历史记录失败: {e}")

    def _save_special_numbers_to_file(self, special_numbers: List[SpecialNumber]) -> None:
        """保存特殊号码到文件"""
        try:
            # 添加到历史记录
            for sn in special_numbers:
                record = {
                    'number': sn.number,
                    'type': sn.number_type.value,
                    'pattern': sn.pattern,
                    'description': sn.description,
                    'discovered_time': datetime.now().isoformat()
                }
                self.special_numbers_history.append(record)

            # 保存到CSV文件
            if self.special_numbers_history:
                df = pd.DataFrame(self.special_numbers_history)
                df.to_csv(self.results_file_path, index=False, encoding='utf-8')

        except Exception as e:
            logger.error(f"保存特殊号码到文件失败: {e}")

    def save_special_numbers(self, special_numbers: List[SpecialNumber]) -> List[SpecialNumber]:
        """保存特殊号码，并返回新发现的号码"""
        if not special_numbers:
            return []

        newly_discovered = []

        if self.use_mysql and self.db_manager:
            def save_to_db(connection):
                nonlocal newly_discovered
                with connection.cursor() as cursor:
                    query = """
                    INSERT IGNORE INTO special_numbers
                    (number, type, pattern, description, city_code, city_name, area_code, phone_attribution, discovered_time)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    for sn in special_numbers:
                        import json
                        data = (
                            sn.number,
                            sn.number_type.value,
                            sn.pattern,
                            sn.description,
                            sn.city_code,
                            sn.city_name,
                            sn.area_code,
                            json.dumps(sn.phone_attribution) if sn.phone_attribution else None,
                            datetime.now()
                        )
                        cursor.execute(query, data)
                        if cursor.rowcount > 0:  # rowcount > 0表示插入成功
                            newly_discovered.append(sn)

            try:
                self.db_manager.execute_with_retry(save_to_db)
                if newly_discovered:
                    logger.info(f"成功保存 {len(newly_discovered)} 个新特殊号码到数据库")
            except Exception as e:
                logger.error(f"保存特殊号码到数据库失败: {e}")
        else:
            # 文件存储模式 - 检查重复
            existing_numbers = {record['number'] for record in self.special_numbers_history}
            for sn in special_numbers:
                if sn.number not in existing_numbers:
                    newly_discovered.append(sn)

            if newly_discovered:
                self._save_special_numbers_to_file(newly_discovered)
                logger.info(f"成功保存 {len(newly_discovered)} 个新特殊号码到文件")

        return newly_discovered

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if self.use_mysql and self.db_manager:
            def get_stats(connection):
                import pymysql.cursors
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute("SELECT COUNT(*) as total FROM processed_numbers")
                    total_processed = cursor.fetchone()['total']

                    cursor.execute("SELECT COUNT(*) as total FROM special_numbers")
                    total_special = cursor.fetchone()['total']

                    cursor.execute("SELECT type, COUNT(*) as count FROM special_numbers GROUP BY type")
                    type_distribution_list = cursor.fetchall()
                    type_distribution = {item['type']: item['count'] for item in type_distribution_list}

                    return {
                        'total_processed': total_processed,
                        'total_special': total_special,
                        'type_distribution': type_distribution,
                        'last_updated': datetime.now().isoformat()
                    }

            try:
                return self.db_manager.execute_with_retry(get_stats)
            except Exception as e:
                logger.error(f"获取统计信息失败: {e}")
                return {'total_processed': 0, 'total_special': 0, 'type_distribution': {}}
        else:
            # 文件存储模式
            type_distribution = {}
            for record in self.special_numbers_history:
                type_name = record['type']
                type_distribution[type_name] = type_distribution.get(type_name, 0) + 1

            return {
                'total_processed': len(self.processed_numbers),
                'total_special': len(self.special_numbers_history),
                'type_distribution': type_distribution,
                'last_updated': datetime.now().isoformat()
            }

    def export_to_json(self, output_file: Optional[str] = None) -> str:
        """导出数据到JSON文件"""
        try:
            if self.use_mysql and self.db_manager:
                def get_export_data(connection):
                    import pymysql.cursors
                    with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                        cursor.execute("SELECT number, type, pattern, description, discovered_time FROM special_numbers ORDER BY discovered_time DESC")
                        records = cursor.fetchall()

                        # 格式化日期
                        for record in records:
                            if hasattr(record['discovered_time'], 'isoformat'):
                                record['discovered_time'] = record['discovered_time'].isoformat()

                        return records

                records = self.db_manager.execute_with_retry(get_export_data)
            else:
                # 文件存储模式
                records = self.special_numbers_history.copy()

            export_data = {
                'statistics': self.get_statistics(),
                'special_numbers': records,
                'export_time': datetime.now().isoformat()
            }

            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=4)
                logger.info(f"数据已成功导出到 {output_file}")
                return output_file
            else:
                return json.dumps(export_data, ensure_ascii=False, indent=4)

        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return ""

    def close(self):
        """关闭数据存储"""
        if self.db_manager:
            self.db_manager.close()
            logger.info("数据库连接已关闭")
        elif not self.use_mysql:
            # 文件存储模式，保存最终状态
            self._save_history_to_file()
            logger.info("文件存储已保存")
