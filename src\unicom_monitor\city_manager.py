"""
城市配置管理模块

负责管理多城市配置、规则启用/禁用等功能。
"""

import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from .config import Config


logger = logging.getLogger(__name__)


@dataclass
class CityConfig:
    """城市配置"""
    city_code: str
    city_name: str
    area_code: str
    province_code: str = "17"
    enabled_rules: List[str] = None
    disabled_rules: List[str] = None
    
    def __post_init__(self):
        if self.enabled_rules is None:
            self.enabled_rules = [
                "leopard",      # 豹子号
                "straight",     # 顺子号  
                "repeat",       # 重复号
                "aabb",         # AABB模式
                "abab",         # ABAB模式
                "mirror",       # 回旋号
            ]
        if self.disabled_rules is None:
            self.disabled_rules = []
    
    def is_rule_enabled(self, rule_name: str) -> bool:
        """检查规则是否启用"""
        if rule_name in self.disabled_rules:
            return False
        return rule_name in self.enabled_rules
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "city_code": self.city_code,
            "city_name": self.city_name,
            "area_code": self.area_code,
            "province_code": self.province_code,
            "enabled_rules": self.enabled_rules,
            "disabled_rules": self.disabled_rules
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CityConfig':
        """从字典创建"""
        return cls(**data)


class CityManager:
    """城市管理器"""
    
    def __init__(self, config: Config, db_manager=None):
        """
        初始化城市管理器
        
        Args:
            config: 配置对象
            db_manager: 数据库管理器
        """
        self.config = config
        self.db_manager = db_manager
        self.cities: Dict[str, CityConfig] = {}
        
        # 加载城市配置
        self._load_cities_config()
        
        # 创建城市配置表
        if self.db_manager:
            self._create_city_config_table()
            self._sync_cities_to_db()
    
    def _load_cities_config(self) -> None:
        """加载城市配置"""
        try:
            # 尝试从配置文件加载
            cities_config_str = getattr(self.config, 'cities_config', None)
            if cities_config_str:
                cities_data = json.loads(cities_config_str)
                for city_code, city_data in cities_data.items():
                    city_data['city_code'] = city_code
                    self.cities[city_code] = CityConfig.from_dict(city_data)
                logger.info(f"从配置加载了 {len(self.cities)} 个城市")
                return
        except Exception as e:
            logger.warning(f"从配置加载城市失败: {e}")
        
        # 使用默认配置
        self._load_default_cities()
    
    def _load_default_cities(self) -> None:
        """加载默认城市配置"""
        default_cities = {
            "166": CityConfig(
                city_code="166",
                city_name="青岛",
                area_code="0532",
                enabled_rules=["leopard", "straight", "repeat", "aabb", "abab", "mirror"],
                disabled_rules=["area_code_embed"]
            ),
            "158": CityConfig(
                city_code="158", 
                city_name="济宁",
                area_code="0537",
                enabled_rules=["leopard", "straight", "repeat", "aabb", "abab", "mirror", "area_code_embed"],
                disabled_rules=[]
            )
        }
        
        self.cities = default_cities
        logger.info(f"加载了 {len(self.cities)} 个默认城市配置")
    
    def _create_city_config_table(self) -> None:
        """创建城市配置表"""
        if not self.db_manager:
            return
            
        def create_table(connection):
            with connection.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS city_configs (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        city_code VARCHAR(10) NOT NULL UNIQUE,
                        city_name VARCHAR(50) NOT NULL,
                        area_code VARCHAR(10),
                        province_code VARCHAR(10) DEFAULT '17',
                        enabled_rules JSON,
                        disabled_rules JSON,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_city_code (city_code)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """)
        
        try:
            self.db_manager.execute_with_retry(create_table)
            logger.info("城市配置表创建/检查完成")
        except Exception as e:
            logger.error(f"创建城市配置表失败: {e}")
    
    def _sync_cities_to_db(self) -> None:
        """同步城市配置到数据库"""
        if not self.db_manager:
            return
            
        def sync_cities(connection):
            with connection.cursor() as cursor:
                for city_code, city_config in self.cities.items():
                    cursor.execute("""
                        INSERT INTO city_configs 
                        (city_code, city_name, area_code, province_code, enabled_rules, disabled_rules)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                        city_name = VALUES(city_name),
                        area_code = VALUES(area_code),
                        province_code = VALUES(province_code),
                        enabled_rules = VALUES(enabled_rules),
                        disabled_rules = VALUES(disabled_rules),
                        updated_at = CURRENT_TIMESTAMP
                    """, (
                        city_config.city_code,
                        city_config.city_name,
                        city_config.area_code,
                        city_config.province_code,
                        json.dumps(city_config.enabled_rules),
                        json.dumps(city_config.disabled_rules)
                    ))
        
        try:
            self.db_manager.execute_with_retry(sync_cities)
            logger.info("城市配置已同步到数据库")
        except Exception as e:
            logger.error(f"同步城市配置到数据库失败: {e}")
    
    def get_city_config(self, city_code: str) -> Optional[CityConfig]:
        """获取城市配置"""
        return self.cities.get(city_code)
    
    def get_all_cities(self) -> Dict[str, CityConfig]:
        """获取所有城市配置"""
        return self.cities.copy()
    
    def get_enabled_cities(self) -> List[str]:
        """获取启用的城市代码列表"""
        return list(self.cities.keys())
    
    def add_city(self, city_config: CityConfig) -> bool:
        """添加城市配置"""
        try:
            self.cities[city_config.city_code] = city_config
            
            if self.db_manager:
                self._sync_cities_to_db()
            
            logger.info(f"添加城市配置: {city_config.city_name} ({city_config.city_code})")
            return True
        except Exception as e:
            logger.error(f"添加城市配置失败: {e}")
            return False
    
    def update_city_rules(self, city_code: str, enabled_rules: List[str], disabled_rules: List[str] = None) -> bool:
        """更新城市规则配置"""
        if city_code not in self.cities:
            logger.error(f"城市 {city_code} 不存在")
            return False
        
        try:
            city_config = self.cities[city_code]
            city_config.enabled_rules = enabled_rules
            if disabled_rules is not None:
                city_config.disabled_rules = disabled_rules
            
            if self.db_manager:
                self._sync_cities_to_db()
            
            logger.info(f"更新城市 {city_config.city_name} 的规则配置")
            return True
        except Exception as e:
            logger.error(f"更新城市规则配置失败: {e}")
            return False
    
    def is_rule_enabled_for_city(self, city_code: str, rule_name: str) -> bool:
        """检查规则是否对指定城市启用"""
        city_config = self.get_city_config(city_code)
        if not city_config:
            return False
        return city_config.is_rule_enabled(rule_name)
    
    def get_city_name(self, city_code: str) -> str:
        """获取城市名称"""
        city_config = self.get_city_config(city_code)
        return city_config.city_name if city_config else f"未知城市({city_code})"
    
    def get_area_code(self, city_code: str) -> str:
        """获取城市区号"""
        city_config = self.get_city_config(city_code)
        return city_config.area_code if city_config else ""
