#!/usr/bin/env python3
"""
联通手机号监控系统 Web 管理界面

提供完整的Web界面来管理监控系统，包括城市配置、监控控制、数据展示等功能。
"""

import os
import sys
import json
import logging
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_socketio import SocketIO, emit
import plotly.graph_objs as go
import plotly.utils

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from unicom_monitor.config import Config
from unicom_monitor.monitor import PhoneNumberMonitor
from unicom_monitor.city_manager import CityManager, CityConfig
from unicom_monitor.data_storage import DataStorage
from unicom_monitor.db_manager import DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'unicom_monitor_secret_key_2025'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
monitor_instance: Optional[PhoneNumberMonitor] = None
monitor_thread: Optional[threading.Thread] = None
monitor_running = False
config: Optional[Config] = None
city_manager: Optional[CityManager] = None
data_storage: Optional[DataStorage] = None


def init_app():
    """初始化应用"""
    global config, city_manager, data_storage
    
    try:
        # 加载配置
        config = Config("config.env")
        
        # 初始化数据存储
        data_storage = DataStorage(config)
        
        # 初始化城市管理器
        city_manager = CityManager(config, data_storage.db_manager if data_storage.use_mysql else None)
        
        logger.info("Web应用初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"Web应用初始化失败: {e}")
        return False


@app.route('/')
def index():
    """首页 - 监控控制面板"""
    try:
        # 获取系统状态
        system_status = get_system_status()
        
        # 获取统计数据
        stats = data_storage.get_statistics() if data_storage else {}
        
        # 获取最近的特殊号码
        recent_numbers = get_recent_special_numbers(limit=10)
        
        return render_template('index.html', 
                             system_status=system_status,
                             stats=stats,
                             recent_numbers=recent_numbers)
    except Exception as e:
        logger.error(f"首页加载失败: {e}")
        flash(f"页面加载失败: {e}", 'error')
        return render_template('error.html', error=str(e))


@app.route('/cities')
def cities():
    """城市配置管理页面"""
    try:
        cities_config = city_manager.get_all_cities() if city_manager else {}
        
        # 获取所有可用的规则
        available_rules = [
            {'id': 'leopard', 'name': '豹子号', 'description': '连续相同数字'},
            {'id': 'straight', 'name': '顺子号', 'description': '连续递增/递减数字'},
            {'id': 'repeat', 'name': '重复号', 'description': '重复数字模式'},
            {'id': 'aabb', 'name': 'AABB模式', 'description': 'AABB数字模式'},
            {'id': 'abab', 'name': 'ABAB模式', 'description': 'ABAB数字模式'},
            {'id': 'mirror', 'name': '镜像号码', 'description': '回文数字'},
            {'id': 'area_code_embed', 'name': '区号嵌入', 'description': '区号嵌入在号码中'},
        ]
        
        return render_template('cities.html', 
                             cities=cities_config,
                             available_rules=available_rules)
    except Exception as e:
        logger.error(f"城市配置页面加载失败: {e}")
        flash(f"页面加载失败: {e}", 'error')
        return redirect(url_for('index'))


@app.route('/statistics')
def statistics():
    """统计数据页面"""
    try:
        # 获取统计数据
        stats = data_storage.get_statistics() if data_storage else {}
        
        # 获取按城市分组的统计
        city_stats = get_city_statistics()
        
        # 获取按时间分组的统计
        time_stats = get_time_statistics()
        
        # 生成图表
        charts = generate_charts(stats, city_stats, time_stats)
        
        return render_template('statistics.html',
                             stats=stats,
                             city_stats=city_stats,
                             time_stats=time_stats,
                             charts=charts)
    except Exception as e:
        logger.error(f"统计页面加载失败: {e}")
        flash(f"页面加载失败: {e}", 'error')
        return redirect(url_for('index'))


@app.route('/logs')
def logs():
    """日志查看页面"""
    try:
        # 获取日志文件列表
        log_files = get_log_files()
        
        return render_template('logs.html', log_files=log_files)
    except Exception as e:
        logger.error(f"日志页面加载失败: {e}")
        flash(f"页面加载失败: {e}", 'error')
        return redirect(url_for('index'))


@app.route('/settings')
def settings():
    """系统设置页面"""
    try:
        # 获取当前配置
        current_config = {
            'request_interval': config.request_interval if config else 20,
            'dingtalk_webhook': config.dingtalk_webhook if config else '',
            'dingtalk_secret': config.dingtalk_secret if config else '',
            'db_host': config.db_host if config else '',
            'db_port': config.db_port if config else 3306,
            'db_name': config.db_name if config else '',
            'db_user': config.db_user if config else '',
        }
        
        return render_template('settings.html', config=current_config)
    except Exception as e:
        logger.error(f"设置页面加载失败: {e}")
        flash(f"页面加载失败: {e}", 'error')
        return redirect(url_for('index'))


def get_system_status() -> Dict[str, Any]:
    """获取系统状态"""
    global monitor_running, monitor_instance
    
    status = {
        'running': monitor_running,
        'current_city': config.city_code if config else '',
        'current_city_name': '',
        'interval': config.request_interval if config else 20,
        'token_status': 'unknown',
        'token_expires_at': '',
        'uptime': '',
        'last_check': '',
        'database_status': 'disconnected'
    }
    
    # 获取城市名称
    if city_manager and config:
        city_config = city_manager.get_city_config(config.city_code)
        if city_config:
            status['current_city_name'] = city_config.city_name
    
    # 获取数据库状态
    if data_storage and data_storage.use_mysql:
        status['database_status'] = 'connected' if data_storage.db_manager.is_healthy() else 'error'
    elif data_storage:
        status['database_status'] = 'file_storage'
    
    # 获取Token状态
    if monitor_instance and hasattr(monitor_instance, 'api_client'):
        api_client = monitor_instance.api_client
        if hasattr(api_client, 'token_manager'):
            token_manager = api_client.token_manager
            if token_manager.is_token_valid():
                status['token_status'] = 'valid'
                if token_manager.token_expires_at:
                    status['token_expires_at'] = token_manager.token_expires_at.strftime('%Y-%m-%d %H:%M:%S')
            else:
                status['token_status'] = 'expired'
    
    return status


def get_recent_special_numbers(limit: int = 10) -> List[Dict[str, Any]]:
    """获取最近发现的特殊号码"""
    if not data_storage or not data_storage.use_mysql:
        return []
    
    try:
        def get_recent(connection):
            import pymysql.cursors
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT number, type, pattern, description, city_name, area_code, discovered_time
                    FROM special_numbers 
                    ORDER BY discovered_time DESC 
                    LIMIT %s
                """, (limit,))
                return cursor.fetchall()
        
        return data_storage.db_manager.execute_with_retry(get_recent)
    except Exception as e:
        logger.error(f"获取最近特殊号码失败: {e}")
        return []


def get_city_statistics() -> Dict[str, Any]:
    """获取按城市分组的统计"""
    if not data_storage or not data_storage.use_mysql:
        return {}
    
    try:
        def get_stats(connection):
            import pymysql.cursors
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT city_name, city_code, COUNT(*) as count
                    FROM special_numbers 
                    WHERE city_name IS NOT NULL
                    GROUP BY city_name, city_code
                    ORDER BY count DESC
                """)
                return cursor.fetchall()
        
        results = data_storage.db_manager.execute_with_retry(get_stats)
        return {item['city_name']: item['count'] for item in results}
    except Exception as e:
        logger.error(f"获取城市统计失败: {e}")
        return {}


def get_time_statistics() -> Dict[str, Any]:
    """获取按时间分组的统计"""
    if not data_storage or not data_storage.use_mysql:
        return {}
    
    try:
        def get_stats(connection):
            import pymysql.cursors
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT DATE(discovered_time) as date, COUNT(*) as count
                    FROM special_numbers 
                    WHERE discovered_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                    GROUP BY DATE(discovered_time)
                    ORDER BY date DESC
                """)
                return cursor.fetchall()
        
        results = data_storage.db_manager.execute_with_retry(get_stats)
        return {str(item['date']): item['count'] for item in results}
    except Exception as e:
        logger.error(f"获取时间统计失败: {e}")
        return {}


def generate_charts(stats: Dict, city_stats: Dict, time_stats: Dict) -> Dict[str, str]:
    """生成图表"""
    charts = {}
    
    try:
        # 类型分布饼图
        if stats.get('type_distribution'):
            fig = go.Figure(data=[go.Pie(
                labels=list(stats['type_distribution'].keys()),
                values=list(stats['type_distribution'].values()),
                hole=0.3
            )])
            fig.update_layout(title="特殊号码类型分布")
            charts['type_distribution'] = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
        
        # 城市分布柱状图
        if city_stats:
            fig = go.Figure(data=[go.Bar(
                x=list(city_stats.keys()),
                y=list(city_stats.values())
            )])
            fig.update_layout(title="按城市分布", xaxis_title="城市", yaxis_title="数量")
            charts['city_distribution'] = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
        
        # 时间趋势图
        if time_stats:
            dates = list(time_stats.keys())
            counts = list(time_stats.values())
            fig = go.Figure(data=[go.Scatter(
                x=dates,
                y=counts,
                mode='lines+markers'
            )])
            fig.update_layout(title="发现趋势（最近30天）", xaxis_title="日期", yaxis_title="数量")
            charts['time_trend'] = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
            
    except Exception as e:
        logger.error(f"生成图表失败: {e}")
    
    return charts


def get_log_files() -> List[Dict[str, Any]]:
    """获取日志文件列表"""
    log_files = []
    logs_dir = Path("logs")
    
    if logs_dir.exists():
        for log_file in logs_dir.glob("*.log"):
            try:
                stat = log_file.stat()
                log_files.append({
                    'name': log_file.name,
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
            except Exception as e:
                logger.error(f"读取日志文件信息失败: {e}")
    
    return sorted(log_files, key=lambda x: x['modified'], reverse=True)


# API路由
@app.route('/api/monitor/start', methods=['POST'])
def start_monitor():
    """启动监控"""
    global monitor_instance, monitor_thread, monitor_running

    try:
        if monitor_running:
            return jsonify({'success': False, 'message': '监控已在运行中'})

        # 创建监控实例
        monitor_instance = PhoneNumberMonitor("config.env")

        # 启动监控线程 - 使用自定义监控循环避免信号处理问题
        def run_monitor():
            global monitor_running
            monitor_running = True
            try:
                # 不使用 monitor_instance.start()，而是直接运行监控循环
                logger.info("Web监控线程启动")

                # 初始化Token
                if not monitor_instance.api_client.unicom_token:
                    if not monitor_instance.api_client._initialize_token():
                        logger.error("无法启动监控，因为首次Token获取失败")
                        monitor_running = False
                        return

                # 监控循环
                while monitor_running:
                    try:
                        # 获取手机号码
                        logger.info("正在获取手机号码...")
                        phone_numbers = monitor_instance.api_client.get_numbers()

                        if not phone_numbers:
                            logger.warning("未获取到任何手机号码")
                        else:
                            logger.info(f"获取到 {len(phone_numbers)} 个手机号码")

                            # 过滤新号码
                            new_numbers = [
                                num for num in phone_numbers
                                if not monitor_instance.storage.is_number_processed(num)
                            ]

                            if new_numbers:
                                logger.info(f"发现 {len(new_numbers)} 个新号码")

                                # 分析特殊号码
                                special_numbers = monitor_instance.analyzer.analyze_numbers(new_numbers, monitor_instance.config.city_code)

                                # 保存新发现的特殊号码
                                new_special_numbers = monitor_instance.storage.save_special_numbers(special_numbers)

                                # 标记号码为已处理
                                monitor_instance.storage.mark_numbers_processed(new_numbers)

                                # 发送通知
                                if new_special_numbers:
                                    logger.info(f"发现 {len(new_special_numbers)} 个新的特殊号码")
                                    success = monitor_instance.notifier.notify_special_numbers(new_special_numbers)
                                    if not success:
                                        logger.warning("钉钉通知发送失败")

                                    # 通知前端新发现的特殊号码
                                    socketio.emit('new_special_numbers', [
                                        {
                                            'number': sn.number,
                                            'type': sn.number_type.value,
                                            'pattern': sn.pattern,
                                            'description': sn.description,
                                            'city_name': sn.city_name,
                                            'discovered_time': datetime.now().isoformat()
                                        } for sn in new_special_numbers
                                    ])
                                else:
                                    logger.info("没有新的特殊号码")
                            else:
                                logger.info("没有新号码需要处理")

                        # 等待下次检查
                        if monitor_running:
                            logger.info(f"等待 {monitor_instance.config.request_interval} 秒后进行下次检查...")
                            time.sleep(monitor_instance.config.request_interval)

                    except Exception as e:
                        logger.error(f"监控循环中发生错误: {e}")
                        if monitor_running:
                            logger.info(f"等待 {monitor_instance.config.retry_delay} 秒后重试...")
                            time.sleep(monitor_instance.config.retry_delay)

                logger.info("Web监控线程结束")

            except Exception as e:
                logger.error(f"监控运行错误: {e}")
            finally:
                monitor_running = False

        monitor_thread = threading.Thread(target=run_monitor, daemon=True)
        monitor_thread.start()

        # 通知前端状态更新
        socketio.emit('status_update', get_system_status())

        return jsonify({'success': True, 'message': '监控已启动'})

    except Exception as e:
        logger.error(f"启动监控失败: {e}")
        return jsonify({'success': False, 'message': f'启动失败: {e}'})


@app.route('/api/monitor/stop', methods=['POST'])
def stop_monitor():
    """停止监控"""
    global monitor_instance, monitor_running

    try:
        if not monitor_running:
            return jsonify({'success': False, 'message': '监控未在运行'})

        if monitor_instance:
            monitor_instance.stop()

        monitor_running = False

        # 通知前端状态更新
        socketio.emit('status_update', get_system_status())

        return jsonify({'success': True, 'message': '监控已停止'})

    except Exception as e:
        logger.error(f"停止监控失败: {e}")
        return jsonify({'success': False, 'message': f'停止失败: {e}'})


@app.route('/api/monitor/status')
def monitor_status():
    """获取监控状态"""
    return jsonify(get_system_status())


@app.route('/api/cities', methods=['GET'])
def get_cities():
    """获取城市配置"""
    try:
        cities = city_manager.get_all_cities() if city_manager else {}
        cities_data = {}

        for city_code, city_config in cities.items():
            cities_data[city_code] = city_config.to_dict()

        return jsonify({'success': True, 'data': cities_data})
    except Exception as e:
        logger.error(f"获取城市配置失败: {e}")
        return jsonify({'success': False, 'message': str(e)})


@app.route('/api/cities', methods=['POST'])
def add_city():
    """添加城市配置"""
    try:
        data = request.get_json()

        city_config = CityConfig(
            city_code=data['city_code'],
            city_name=data['city_name'],
            area_code=data['area_code'],
            province_code=data.get('province_code', '17'),
            enabled_rules=data.get('enabled_rules', []),
            disabled_rules=data.get('disabled_rules', [])
        )

        if city_manager.add_city(city_config):
            return jsonify({'success': True, 'message': '城市添加成功'})
        else:
            return jsonify({'success': False, 'message': '城市添加失败'})

    except Exception as e:
        logger.error(f"添加城市失败: {e}")
        return jsonify({'success': False, 'message': str(e)})


@app.route('/api/cities/<city_code>', methods=['PUT'])
def update_city(city_code):
    """更新城市配置"""
    try:
        data = request.get_json()

        enabled_rules = data.get('enabled_rules', [])
        disabled_rules = data.get('disabled_rules', [])

        if city_manager.update_city_rules(city_code, enabled_rules, disabled_rules):
            return jsonify({'success': True, 'message': '城市配置更新成功'})
        else:
            return jsonify({'success': False, 'message': '城市配置更新失败'})

    except Exception as e:
        logger.error(f"更新城市配置失败: {e}")
        return jsonify({'success': False, 'message': str(e)})


@app.route('/api/statistics')
def get_statistics_api():
    """获取统计数据API"""
    try:
        stats = data_storage.get_statistics() if data_storage else {}
        city_stats = get_city_statistics()
        time_stats = get_time_statistics()

        return jsonify({
            'success': True,
            'data': {
                'general': stats,
                'by_city': city_stats,
                'by_time': time_stats
            }
        })
    except Exception as e:
        logger.error(f"获取统计数据失败: {e}")
        return jsonify({'success': False, 'message': str(e)})


@app.route('/api/logs/<filename>')
def get_log_content(filename):
    """获取日志文件内容"""
    try:
        log_file = Path("logs") / filename
        if not log_file.exists():
            return jsonify({'success': False, 'message': '日志文件不存在'})

        # 读取最后1000行
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            recent_lines = lines[-1000:] if len(lines) > 1000 else lines

        return jsonify({
            'success': True,
            'data': {
                'content': ''.join(recent_lines),
                'total_lines': len(lines),
                'showing_lines': len(recent_lines)
            }
        })
    except Exception as e:
        logger.error(f"读取日志文件失败: {e}")
        return jsonify({'success': False, 'message': str(e)})


@app.route('/api/settings', methods=['POST'])
def update_settings():
    """更新系统设置"""
    try:
        data = request.get_json()

        # 这里应该更新配置文件
        # 为了简化，我们只返回成功消息
        # 实际应用中需要更新config.env文件

        return jsonify({'success': True, 'message': '设置更新成功'})
    except Exception as e:
        logger.error(f"更新设置失败: {e}")
        return jsonify({'success': False, 'message': str(e)})


# WebSocket事件
@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    logger.info("客户端已连接")
    emit('status_update', get_system_status())


@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接"""
    logger.info("客户端已断开连接")


@socketio.on('request_status')
def handle_status_request():
    """客户端请求状态更新"""
    emit('status_update', get_system_status())


# 定时任务：定期推送状态更新
def background_task():
    """后台任务：定期推送数据更新"""
    while True:
        try:
            if monitor_running:
                # 推送状态更新
                socketio.emit('status_update', get_system_status())

                # 推送最新的特殊号码
                recent_numbers = get_recent_special_numbers(5)
                if recent_numbers:
                    socketio.emit('new_special_numbers', recent_numbers)

            time.sleep(10)  # 每10秒更新一次
        except Exception as e:
            logger.error(f"后台任务错误: {e}")
            time.sleep(30)


# 启动后台任务
background_thread = threading.Thread(target=background_task, daemon=True)
background_thread.start()


if __name__ == '__main__':
    if init_app():
        print("🌐 联通手机号监控系统 Web 管理界面")
        print("=" * 50)
        print("🚀 启动Web服务器...")
        print("📱 访问地址: http://localhost:5000")
        print("⚡ 支持实时数据更新")
        print("🛠️ 功能完整的管理界面")
        print("=" * 50)
        
        socketio.run(app, host='0.0.0.0', port=5000, debug=False)
    else:
        print("❌ Web应用初始化失败，请检查配置")
        sys.exit(1)
