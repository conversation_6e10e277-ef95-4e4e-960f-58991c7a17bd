"""
网络工具模块

提供网络重试、连接检测等功能。
"""

import time
import socket
import logging
from typing import Callable, Any, Optional
import requests
from requests.exceptions import RequestException, Timeout, ConnectionError


logger = logging.getLogger(__name__)


class NetworkRetryMixin:
    """网络重试混入类"""
    
    def retry_with_backoff(
        self, 
        func: Callable[[], Any], 
        operation_name: str,
        max_retries: int = 5,
        base_delay: float = 2.0,
        max_delay: float = 60.0,
        timeout_retries: int = 5
    ) -> Any:
        """
        使用指数退避策略重试网络操作
        
        Args:
            func: 要重试的函数
            operation_name: 操作名称（用于日志）
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            timeout_retries: 超时错误的额外重试次数
            
        Returns:
            函数执行结果
            
        Raises:
            Exception: 所有重试都失败后抛出最后一个异常
        """
        last_exception = None
        timeout_count = 0
        
        for attempt in range(max_retries):
            try:
                logger.debug(f"{operation_name} - 尝试 {attempt + 1}/{max_retries}")
                result = func()
                if attempt > 0:
                    logger.info(f"{operation_name} - 重试成功")
                return result
                
            except (Timeout, socket.timeout) as e:
                timeout_count += 1
                last_exception = e
                
                # 对于超时错误，给予额外的重试机会
                if timeout_count <= timeout_retries:
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    logger.warning(f"{operation_name} - 超时错误 (第{timeout_count}次)，{delay}秒后重试: {e}")
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"{operation_name} - 超时重试次数已达上限")
                    break
                    
            except (ConnectionError, requests.exceptions.ConnectionError) as e:
                last_exception = e
                
                # 检查网络连接
                if not self.check_network_connectivity():
                    logger.warning(f"{operation_name} - 网络不可用，等待网络恢复...")
                    self.wait_for_network_recovery()
                
                if attempt < max_retries - 1:
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    logger.warning(f"{operation_name} - 连接错误，{delay}秒后重试: {e}")
                    time.sleep(delay)
                else:
                    logger.error(f"{operation_name} - 连接错误，已达最大重试次数")
                    
            except RequestException as e:
                last_exception = e
                
                if attempt < max_retries - 1:
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    logger.warning(f"{operation_name} - 请求错误，{delay}秒后重试: {e}")
                    time.sleep(delay)
                else:
                    logger.error(f"{operation_name} - 请求错误，已达最大重试次数")
                    
            except Exception as e:
                last_exception = e
                logger.error(f"{operation_name} - 未知错误: {e}")
                break
        
        # 所有重试都失败
        logger.error(f"{operation_name} - 所有重试都失败")
        if last_exception:
            raise last_exception
        else:
            raise Exception(f"{operation_name} 失败")
    
    def check_network_connectivity(self, host: str = "*******", port: int = 53, timeout: float = 3.0) -> bool:
        """
        检查网络连接状态
        
        Args:
            host: 测试主机
            port: 测试端口
            timeout: 超时时间
            
        Returns:
            网络是否可用
        """
        try:
            socket.setdefaulttimeout(timeout)
            socket.socket(socket.AF_INET, socket.SOCK_STREAM).connect((host, port))
            return True
        except socket.error:
            return False
    
    def wait_for_network_recovery(self, check_interval: float = 10.0, max_wait: float = 300.0) -> bool:
        """
        等待网络恢复
        
        Args:
            check_interval: 检查间隔（秒）
            max_wait: 最大等待时间（秒）
            
        Returns:
            网络是否已恢复
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            if self.check_network_connectivity():
                logger.info("网络连接已恢复")
                return True
                
            logger.debug(f"网络仍不可用，{check_interval}秒后重新检查...")
            time.sleep(check_interval)
        
        logger.error(f"等待网络恢复超时（{max_wait}秒）")
        return False


class NetworkMonitor:
    """网络状态监控器"""
    
    def __init__(self):
        self.last_check_time = 0
        self.check_interval = 30  # 30秒检查一次
        self.is_online = True
    
    def is_network_available(self) -> bool:
        """
        检查网络是否可用（带缓存）
        
        Returns:
            网络是否可用
        """
        current_time = time.time()
        
        # 如果距离上次检查时间不足间隔，返回缓存结果
        if current_time - self.last_check_time < self.check_interval:
            return self.is_online
        
        # 执行网络检查
        self.last_check_time = current_time
        try:
            # 尝试连接多个服务器
            test_hosts = [
                ("*******", 53),      # Google DNS
                ("*******", 53),      # Cloudflare DNS
                ("***************", 53)  # 114 DNS
            ]
            
            for host, port in test_hosts:
                try:
                    socket.setdefaulttimeout(3)
                    socket.socket(socket.AF_INET, socket.SOCK_STREAM).connect((host, port))
                    self.is_online = True
                    return True
                except socket.error:
                    continue
            
            # 所有测试都失败
            self.is_online = False
            return False
            
        except Exception as e:
            logger.error(f"网络检查时发生错误: {e}")
            self.is_online = False
            return False
    
    def wait_for_connection(self, timeout: float = 300.0) -> bool:
        """
        等待网络连接恢复
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            是否成功连接
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.is_network_available():
                return True
            time.sleep(10)  # 每10秒检查一次
        
        return False


def test_network_connectivity() -> bool:
    """
    测试网络连接
    
    Returns:
        网络是否可用
    """
    monitor = NetworkMonitor()
    return monitor.is_network_available()


def create_session_with_retry() -> requests.Session:
    """
    创建带重试功能的requests会话
    
    Returns:
        配置好的requests会话
    """
    session = requests.Session()
    
    # 设置默认超时
    session.timeout = 15
    
    # 设置重试适配器
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry
    
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"]
    )
    
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    return session
