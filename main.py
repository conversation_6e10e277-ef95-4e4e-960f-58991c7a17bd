#!/usr/bin/env python3
"""
联通手机号监控系统主程序

Author: alxxxxla
Version: 1.0.0
"""

import sys
import argparse
import logging
from pathlib import Path
import time

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from unicom_monitor.monitor import PhoneNumberMonitor


def main():
    """主函数"""
    # 强制开启基本日志记录，以便捕获最早期的错误
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("logs/monitor.log", encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logging.info("程序启动，开始解析参数...")

    parser = argparse.ArgumentParser(
        description="联通手机号监控系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 启动持续监控
  python main.py --once             # 执行单次检查
  python main.py --test             # 测试钉钉连接
  python main.py --stats            # 显示统计信息
  python main.py --export           # 导出数据
  python main.py --config custom.env # 使用自定义配置文件
        """
    )
    
    parser.add_argument(
        "--config", "-c",
        default="config.env",
        help="配置文件路径 (默认: config.env)"
    )
    
    parser.add_argument(
        "--once",
        action="store_true",
        help="执行单次检查而不是持续监控"
    )
    
    parser.add_argument(
        "--test",
        action="store_true",
        help="测试钉钉连接"
    )
    
    parser.add_argument(
        "--stats",
        action="store_true",
        help="显示统计信息"
    )
    
    parser.add_argument(
        "--export",
        nargs='?', 
        const="export.json", 
        default=None,
        metavar="FILE",
        help="导出数据到指定文件 (默认: export.json)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="启用详细日志输出"
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        print("🐛 调试模式已启用")
        logging.info("已启用调试模式 (DEBUG)")
    elif args.verbose:
        # 如果需要，再提升为DEBUG级别
        logging.getLogger().setLevel(logging.DEBUG)
        logging.info("已启用详细日志模式 (DEBUG)")
    
    try:
        logging.info("正在创建PhoneNumberMonitor实例...")
        if args.debug:
            print(f"🔧 使用配置文件: {args.config}")
        monitor = PhoneNumberMonitor(args.config)
        logging.info("PhoneNumberMonitor实例创建成功。")
        
        if args.test:
            # 测试钉钉连接
            print("正在测试钉钉连接...")
            if monitor.notifier.test_connection():
                print("✅ 钉钉连接测试成功")
            else:
                print("❌ 钉钉连接测试失败")
            return
        
        if args.stats:
            # 显示统计信息
            stats = monitor.get_statistics()
            print("\n📊 统计信息:")
            print(f"  总处理号码: {stats['total_processed']}")
            print(f"  发现特殊号码: {stats['total_special']}")
            if stats['type_distribution']:
                print("\n📱 特殊号码类型分布:")
                for type_name, count in stats['type_distribution'].items():
                    print(f"  - {type_name}: {count}个")
            return
        
        if args.export is not None:
            # 导出数据
            output_file = monitor.export_data(args.export)
            if output_file:
                print(f"✅ 数据已导出到: {output_file}")
            else:
                print("❌ 数据导出失败，请检查日志。")
            return
        
        if args.once:
            # 执行单次检查
            print("执行单次检查...")
            monitor.run_once()
            print("✅ 单次检查完成")
        else:
            # 启动持续监控
            print("启动联通手机号监控系统...")
            print("按 Ctrl+C 停止监控")
            
            monitor.start()
            
            time.sleep(2)  # 等待监控线程初始化

            if not monitor.is_running():
                print("\n❌ 监控启动失败，请检查日志 monitor.log 获取详细错误信息。")
                print("   常见原因：数据库连接失败、ecs_token失效、配置错误等。")
                sys.exit(1)

            try:
                # 保持主线程运行，直到监控停止
                while monitor.is_running():
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n接收到停止信号...")
            finally:
                monitor.stop()
                print("✅ 监控系统已停止")
    
    except FileNotFoundError as e:
        print(f"❌ 配置文件未找到: {e}")
        print("请确保配置文件存在，或使用 --config 指定正确的配置文件路径")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        logging.exception("程序异常退出")
        sys.exit(1)


if __name__ == "__main__":
    main()
